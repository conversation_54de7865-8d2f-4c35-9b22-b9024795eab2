<?php

namespace models;

use Exception;
use models\composite\oAutomatedActions\cancelAutomatedActions;
use models\composite\oAutomatedActions\getAutomatedActionScheduleTimeStamp;
use models\composite\oAutomatedActions\getAutomatedEmailActive;
use models\composite\oAutomatedActions\getAutomatedRuleActionLoanFileId;
use models\composite\oAutomatedActions\getAutomatedTaskActive;
use models\composite\oAutomatedActions\getReferralDateLoanFile;
use models\composite\oAutomatedActions\getTaskDueDate;
use models\composite\oAutomatedActions\getTaskRemainderDate;
use models\composite\oAutomatedActions\previewAutomatedActions;
use models\composite\oAutomatedActions\pushAutomatedRuleChangeFileStatus;
use models\composite\oAutomatedActions\pushAutomatedRuleEmailData;
use models\composite\oAutomatedActions\pushAutomatedRuleTaskData;
use models\composite\oAutomatedActions\pushAutomatedRuleToAssignEmployee;
use models\composite\oAutomatedActions\pushAutomatedRuleWebhookData;
use models\composite\oAutomatedRules\checkAutomatedRulesForLoanFile_orig;
use models\composite\oFile\getFileInfo;
use models\composite\oWebhook\getWebhookParamData;
use models\constants\automationConstants;
use models\Controllers\backoffice\automation;
use models\LeadReceiver\LeadReceiverData;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

class automatedRulesActionController extends strongType
{
    public static function fromGlobals()
    {
        //global variables
        global $LMRId, $PCID, $primeStatusId, $fileSubStatusId, $userTimeZone;
        global $changeStatusTo;
        global $allowRepeat, $triggerRule;

        self::Go(
            $LMRId,
            $PCID,
            $primeStatusId,
            $fileSubStatusId,
            $userTimeZone,
            $changeStatusTo,
            $allowRepeat,
            $triggerRule,
            null,
            null
        );
    }

    public static function Go(
        $LMRId,
        $PCID,
        $primeStatusId,
        $fileSubStatusId,
        $userTimeZone,
        $changeStatusTo,
        $allowRepeat,
        $triggerRule,
        $fileTypesTxt,
        $leadReceiver
    )
    {

//UserTimeZone
        $ipArray = [];
        $ipArray['inputZone'] = $userTimeZone;
        $ipArray['outputZone'] = CONST_SERVER_TIME_ZONE;
        $ipArray['inputTime'] = Dates::Timestamp();
        $dateTimeNow = Dates::timeZoneConversion($ipArray);

        $publicUser = '';
        if (isset($_POST['publicUser'])) $publicUser = $_POST['publicUser'];
        if (!isset($fileTypesTxt) || !$fileTypesTxt) {
            $fileTypesTxt = Request::isset('fileTypesTxt') ? Request::GetClean('fileTypesTxt') : '';
        }
        if ($publicUser) {
            $fileTypesTxt = $_POST['ft']; //file type from Web form
            $_POST['dataChanged'] = 'Yes';
        }
//check if we have any Automated Rules (with Conditions) created for PFS/FSS/Workflow
        /* IF YES */
//check if we have any Automated Actions (for the above rule) Instant / Schedule Action
//checks if the file status is updated
//the code logic here for FSS update only by adding an OR condition (for below if)
        $isFssUpdated = null;
        if (isset($_POST['isFssUpdated'])) {
            $isFssUpdated = $_POST['isFssUpdated'];
        }
//Primary File Status (from the UI)
        $PFS = '';
        if (isset($_POST['primaryStatus'])) {
            $PFS = trim($_POST['primaryStatus']);
        }
//ADMIN INFO TAB
        $activeTab = '';
        if (isset($_POST['activeTab'])) {
            $activeTab = $_POST['activeTab'];
        }

        if ($activeTab == 'ADMIN') {
            if (isset($_POST['statusId'])) $PFS = trim($_POST['statusId']);
        }
//lead receiver
        if (isset($leadReceiver) && $leadReceiver == 'YES') {
            $isFssUpdated = 'Yes';
            $triggerRule = 'Yes';
            $_POST['fileRow'] = 'Insert';
        }
//get the latest updated parameter to check the specific condition
//PFS / FSS
//$lastUpdatedParam = $lastUpdatedFss = '';
        $lastUpdatedParam = $_POST['lastUpdatedParam'] ?? '';
        $lastUpdatedFss = $_POST['lastUpdatedFss'] ?? '';

//automation for no of days file status
        $checkAutoNoOfDayStatus = '';
        if (isset($autoNoOfDayStatus) && $autoNoOfDayStatus == 'Yes') {
            $PFS = $changeStatusTo;
            $_POST['OSID'] = $changeStatusTo;
            $lastUpdatedParam = 'PFS';
            $checkAutoNoOfDayStatus = 'Yes';
        }
//New File Tab
        $dataChanged = $_POST['dataChanged'] ?? '';
        $fileRow = $_POST['fileRow'] ?? '';
        if (trim($_POST['OSID']) != $PFS || $isFssUpdated == 'Yes' || $dataChanged = 'Yes') {

            $LMRArray = getFileInfo::getReport(['LMRId' => $LMRId]);
            $myFileInfo = $LMRArray[$LMRId];
            if (!$PCID) $PCID = trim(cypher::myDecryption($_POST['encryptedPCID']));

            $fileSubStatus = $myFileInfo['fileSubstatusInfo'] ?? [];
            $wfsIds = $myFileInfo['WFListArray'][$LMRId] ?? [];

            //PC Settings Send Email From
            $SENDSMSFROM = $myFileInfo['PCInfo']['attorneyCell'];
            if ($SENDSMSFROM) {
                $SENDSMSFROM = '+1' . Strings::numberOnly($SENDSMSFROM);
            }

            //lead receiver
            if (isset($leadReceiver) && $leadReceiver == 'YES') {
                //the below values are from the leadReceiver.php
                $PFS = $primeStatusId;
                $fileSubStatus = [0 => ['substatusId' => $fileSubStatusId]];
                $lastUpdatedParam = 'PFS';
            }

            //Fetch the Rules matching old trigger points
            //There is no trigger point for a new file create
            if (array_intersect(
                    explode(',', strtoupper($lastUpdatedParam)),
                    ['FCU', 'PFS', 'FSS', 'BRANCH', 'BROKER', 'LO', 'BORROWERTYPE', 'LOANPROGRAM', 'INITIALSOW', 'DRAWREQUEST', 'REVISION']
                )
                && $allowRepeat != 'check'
                && $triggerRule == 'Yes'
                && $fileRow != 'Insert'
                && $checkAutoNoOfDayStatus != 'Yes'
            ) {
                $oldPFS = $_POST['OSID'] ?? '';
                if (!$oldPFS) $oldPFS = $_POST['previousModStatusId'] ?? ''; //MyPipeline
                if (!$oldPFS) {
                    return;
                }
                $data = [
                    'PCID'      => $PCID,
                    'fileType'  => $fileTypesTxt,
                    'PFS'       => $oldPFS,
                    'FSS'       => $fileSubStatus,
                    'workflows' => $wfsIds,
                    'lup'       => $lastUpdatedParam,
                    'luFss'     => $lastUpdatedFss,
                    'LMRId'     => $LMRId
                ];
                $result = checkAutomatedRulesForLoanFile_orig::getReport($data) ?? [];
                $ruleData = [];
                if (count($result) > 0) {
                    foreach ($result as $rData) {
                        $tblARId = $rData['tblARId'];
                        $actionId = $rData['actionId'];
                        $contEventStatus = $rData['contEventStatus'];
                        if (!$contEventStatus) {
                            $ruleData[] = [
                                'tblARId'  => $tblARId,
                                'actionId' => $actionId
                            ];
                        }
                    }
                    //Cancel the automated scheduled events
                    $params = [
                        'PCID'        => $PCID,
                        'LMRId'       => $LMRId,
                        'ruleData'    => $ruleData,
                        'location'    => 'PFS',
                        'dateTimeNow' => $dateTimeNow
                    ];
                    cancelAutomatedActions::getReport($params);
                }
            }

            $data = [
                'PCID'              => $PCID,
                'fileType'          => $fileTypesTxt,
                'PFS'               => $PFS,
                'FSS'               => $fileSubStatus,
                'workflows'         => $wfsIds,
                'lup'               => $lastUpdatedParam,
                'luFss'             => $lastUpdatedFss,
                'dataChanged'       => $dataChanged,
                'fileRow'           => $fileRow,
                'autoNoOfDayStatus' => $autoNoOfDayStatus ?? '',
                'noOfDayStatus'     => $noOfDayStatus ?? 0,
                'LMRId'             => $LMRId
            ];
            //pr($data);
            //exit();
            // Check for Automated Rules for this Loan File
            // Automated Actions Master Query (Automated Rules, Automated Actions, Loan File Details)
            $result = checkAutomatedRulesForLoanFile_orig::getReport($data) ?? [];
            /*pr($result);
            exit();*/
            //return the actions for user confirmation
            $resultData = previewAutomatedActions::getReport($result, $LMRId, $PCID, $fileTypesTxt, $activeTab);
            if ($allowRepeat == 'check') {
                echo json_encode($resultData);
                exit();
            }
            $manual = Request::isset('manual') ? Request::GetClean('manual') : 0;
            $autoTaskIds = [];
            $autoEmailIds = [];
            $autoWebhookIds = [];
            $autoEmployeeIds = [];
            $autoChangeFileStatusId = 0;
            if ($manual) {
                $autoTaskIds = $_POST['autoTaskIds'] ? explode(',', $_POST['autoTaskIds']) : [];
                $autoEmailIds = $_POST['autoEmailIds'] ? explode(',', $_POST['autoEmailIds']) : [];
                $autoWebhookIds = $_POST['autoWebhookIds'] ? explode(',', $_POST['autoWebhookIds']) : [];
                $autoEmployeeIds = $_POST['autoEmployeeIds'] ? explode(',', $_POST['autoEmployeeIds']) : [];
                $autoChangeFileStatusId = $_POST['autoChangeFileStatusId'] ?? 0;
                //All actions from the popup
                $allActions = $_POST['allAction'] ?? '';
                if ($allActions) {
                    automation::getUnCheckedActions(
                        $allActions
                        , $autoTaskIds
                        , $autoEmailIds
                        , $autoWebhookIds
                        , $autoEmployeeIds
                        , $autoChangeFileStatusId
                        , $dateTimeNow
                        , $userTimeZone
                        , $activeTab
                    );
                }
            }
            $userAutomationControlAccess = intval($_POST['userAutomationControlAccess']) ?? 0;
            if (count($result ?? []) > 0 && $triggerRule == 'Yes') {
                $autoAction = [];
                foreach ($result as $rule) { // may have multiple rules
                    $ARALFId = getAutomatedRuleActionLoanFileId::getReport(
                        $rule
                        , $manual
                        , $autoWebhookIds
                        , $autoTaskIds
                        , $autoEmailIds
                        , $autoEmployeeIds
                        , $autoChangeFileStatusId
                        , $PCID
                        , $LMRId
                        , $activeTab
                        , $allowRepeat
                        , $triggerRule
                        , $dateTimeNow
                    );
                    if ($ARALFId) { // to check the null;
                        $autoAction[] = $ARALFId;
                        //Webhook
                        if ($rule['action'] == automationConstants::$automation_Webhook) {
                            //get the Webhook details
                            $whid = $rule['actionId'];
                            $whParams = ['PCID' => $PCID, 'WHID' => $whid];
                            $fieldNames = getWebhookParamData::getReport($whParams); // we will get only one webhook
                            $postDataUrl = $fieldNames[$whid]['url'];
                            //Schedule Webhook
                            $actionType = $rule['eventType'];
                            $whScheduleTimeStamp = $whScheduleDate = 0;
                            if ($actionType == automationConstants::$automation_Schedule) {
                                //Schedule Webhook
                                $noOfDays = $postDataUrl[0]['noOfDays'];
                                $eventWhen = $postDataUrl[0]['eventWhen'];
                                $referralDate = $postDataUrl[0]['referralDate'];
                                //get the referral Date from the Loan file
                                $whReferralDateLoanFile = getReferralDateLoanFile::getReport($referralDate, $myFileInfo);

                                if ($whReferralDateLoanFile) {
                                    $schParams = [
                                        'noOfDays'     => $noOfDays,
                                        'eventWhen'    => $eventWhen,
                                        'referralDate' => $whReferralDateLoanFile,
                                        'userTimeZone' => $userTimeZone
                                    ];
                                    $whScheduleTimeStampData = getAutomatedActionScheduleTimeStamp::getReport($schParams);
                                    //pr($ScheduleTimeStamp);
                                    if ($whScheduleTimeStampData) {
                                        $whSchDateTimeStampDe = json_decode($whScheduleTimeStampData);
                                        $whScheduleDate = $whSchDateTimeStampDe->scheduleDate;
                                        $whScheduleTimeStamp = $whSchDateTimeStampDe->scheduleTimeStamp;
                                    }
                                }
                            }
                            $params = [
                                'PCID'        => $PCID,
                                'ARALFId'     => $ARALFId,
                                'LMRId'       => $LMRId,
                                'actionType'  => $actionType,
                                'whid'        => $whid,
                                'scheduleFor' => $whScheduleTimeStamp,
                                'scheduleOn'  => $whScheduleDate
                            ];
                            //we push this data to the post-webhook URL
                            try {
                                pushAutomatedRuleWebhookData::getReport($params);
                            } catch (Exception $e) {
                            }
                        } //Task
                        elseif ($rule['action'] == automationConstants::$automation_Task) {
                            //get the task details
                            $tsid = $rule['actionId'];
                            $tsParams = ['PCID' => $PCID, 'id' => $tsid];
                            $tasks = getAutomatedTaskActive::getReport($tsParams);
                            if (count($tasks) > 0) { // we will get only one task
                                //Schedule Task
                                $taskId = ($tasks['id'] > 0) ? $tasks['id'] : 0;
                                $noOfDaysDD = $tasks['noOfDaysDD'];
                                $eventWhenDD = $tasks['eventWhenDD'];
                                $noOfDaysRD = $tasks['noOfDaysRD'];
                                $eventWhenRD = $tasks['eventWhenRD'];
                                //Send Task Reminder via Email
                                $reminderEmail = $tasks['reminderEmail'];
                                $taskCreatedDate = Dates::Timestamp(); //Instant Task (current datetime)
                                $actionType = $rule['eventType'];
                                $tsScheduleTimeStamp = $tsScheduleDate = 0;
                                if ($actionType == automationConstants::$automation_Schedule) {
                                    //Schedule Task
                                    $noOfDays = $tasks['noOfDays'];
                                    $eventWhen = $tasks['eventWhen'];
                                    $referralDate = $tasks['referralDate'];
                                    //get the referral Date from the Loan file
                                    $tsReferralDateLoanFile = getReferralDateLoanFile::getReport($referralDate, $myFileInfo);
                                    if ($tsReferralDateLoanFile) {
                                        $schParams = [
                                            'noOfDays'     => $noOfDays,
                                            'eventWhen'    => $eventWhen,
                                            'referralDate' => $tsReferralDateLoanFile,
                                            'userTimeZone' => $userTimeZone
                                        ];
                                        $tsScheduleTimeStampData = getAutomatedActionScheduleTimeStamp::getReport($schParams);
                                        if ($tsScheduleTimeStampData) {
                                            $tsSchDateTimeStampDe = json_decode($tsScheduleTimeStampData);
                                            $tsScheduleDate = $tsSchDateTimeStampDe->scheduleDate;
                                            $tsScheduleTimeStamp = $tsSchDateTimeStampDe->scheduleTimeStamp;
                                        }
                                    }
                                    $taskCreatedDate = $tsScheduleDate;  //Schedule Task (Schedule datetime)
                                }

                                //Task Due Date
                                $taskDueDate = '';
                                $tddParams = [
                                    'noOfDaysDD'      => $noOfDaysDD,
                                    'eventWhenDD'     => $eventWhenDD,
                                    'taskCreatedDate' => $taskCreatedDate
                                ];
                                $tddTimeStampData = getTaskDueDate::getReport($tddParams);
                                if ($tddTimeStampData) {
                                    $taskDueDate = $tddTimeStampData;
                                }
                                //Task Remainder Date
                                $taskRemainderDate = '';
                                if ($reminderEmail) {
                                    $trdParams = [
                                        'noOfDaysRD'      => $noOfDaysRD,
                                        'eventWhenRD'     => $eventWhenRD,
                                        'taskCreatedDate' => $taskCreatedDate,
                                        'taskDueDate'     => $taskDueDate
                                    ];
                                    $trdTimeStampData = getTaskRemainderDate::getReport($trdParams);
                                    if ($trdTimeStampData) {
                                        $taskRemainderDate = $trdTimeStampData;
                                    }
                                }
                                $taskData = [
                                    'PCID'              => $PCID,
                                    'ARALFId'           => $ARALFId,
                                    'actionType'        => $actionType,
                                    'fileID'            => $LMRId,
                                    'taskId'            => $taskId,
                                    'scheduleFor'       => $tsScheduleTimeStamp,
                                    'scheduleOn'        => $tsScheduleDate,
                                    'taskDueDate'       => $taskDueDate,
                                    'taskRemainderDate' => $taskRemainderDate,
                                    'userTimeZone'      => $userTimeZone,
                                    'sendSmsFrom'       => $SENDSMSFROM,
                                ];
                                //we push this data to create TASK.
                                try {
                                    pushAutomatedRuleTaskData::getReport($taskData);
                                } catch (Exception $e) {
                                }
                            }
                        } //Email Notification
                        elseif ($rule['action'] == automationConstants::$automation_Email) {
                            //get the email details
                            $emailId = $rule['actionId'];
                            $emailParams = ['PCID' => $PCID, 'id' => $emailId];
                            $email = getAutomatedEmailActive::getReport($emailParams);
                            if (count($email) > 0) { // we will get only one email
                                //Schedule Email
                                $emailId = ($email['id'] > 0) ? $email['id'] : 0;
                                $actionType = $rule['eventType'];
                                $emScheduleDate = $emScheduleTimeStamp = 0;
                                if ($actionType == automationConstants::$automation_Schedule) {
                                    //Schedule Email
                                    $noOfDays = $email['noOfDays'];
                                    $eventWhen = $email['eventWhen'];
                                    $referralDate = $email['referralDate'];
                                    //get the referral Date from the Loan file
                                    $emReferralDateLoanFile = getReferralDateLoanFile::getReport($referralDate, $myFileInfo);
                                    if ($emReferralDateLoanFile) {
                                        $schParams = [
                                            'noOfDays'     => $noOfDays,
                                            'eventWhen'    => $eventWhen,
                                            'referralDate' => $emReferralDateLoanFile,
                                            'userTimeZone' => $userTimeZone
                                        ];
                                        $emScheduleTimeStampData = getAutomatedActionScheduleTimeStamp::getReport($schParams);
                                        //pr($ScheduleTimeStamp);
                                        if ($emScheduleTimeStampData) {
                                            $emSchDateTimeStampDe = json_decode($emScheduleTimeStampData);
                                            $emScheduleDate = $emSchDateTimeStampDe->scheduleDate;
                                            $emScheduleTimeStamp = $emSchDateTimeStampDe->scheduleTimeStamp;
                                        }
                                    }
                                }
                                $emailData = [
                                    'PCID'        => $PCID,
                                    'ARALFId'     => $ARALFId,
                                    'actionType'  => $actionType,
                                    'fileID'      => $LMRId,
                                    'emailId'     => $emailId,
                                    'scheduleFor' => $emScheduleTimeStamp,
                                    'scheduleOn'  => $emScheduleDate,
                                    'dateTimeNow' => $dateTimeNow,
                                ];
                                try {
                                    pushAutomatedRuleEmailData::getReport($emailData);
                                } catch (Exception $e) {
                                }
                            }
                        }
                        elseif ($rule['action'] === automationConstants::$automation_Employee) {
                            pushAutomatedRuleToAssignEmployee::getReport(
                                $PCID
                                , $LMRId
                                , $ARALFId
                                , $rule['actionId'] //actionId
                            );
                        }
                        elseif ($rule['action'] === automationConstants::$automation_change_file_status) {
                            $statusId = $rule['actionId'];
                            pushAutomatedRuleChangeFileStatus::getReport($PCID, $LMRId, $ARALFId, $statusId);
                        }
                    }
                }
                $autoActionCount = count($autoAction);
                if ($autoActionCount && $manual && !$publicUser) {
                    $msg = 'Updated Successfully and ' . $autoActionCount . ' automation action(s) are triggered';
                    Strings::SetSess('msg', $msg);
                }
                if (!$userAutomationControlAccess && !$manual && !$publicUser && $leadReceiver != 'YES') {
                    Strings::SetSess('resultData', json_encode($resultData));
                    Strings::SetSess('lastUpdatedParam', $lastUpdatedParam);
                    Strings::SetSess('autoLMRId', cypher::myEncryption($LMRId));
                }
            }
        }
    }

    public static function fromLeadReceiver(
        ?int $PCID,
        LeadReceiverData $leadReceiverData,
        ?string $userTimeZone,
        ?string $fileTypesTxt
    )
    {
        self::Go(
            $leadReceiverData->LMRId,
            $PCID,
            $leadReceiverData->primeStatusId,
            $leadReceiverData->fileSubStatusId,
            $userTimeZone,
            null,
            null,
            null,
            $fileTypesTxt,
            'YES'
        );
    }
}
