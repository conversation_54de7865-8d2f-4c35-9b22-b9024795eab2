<?php
namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\composite\oDrawManagement\traits\PropertiesMapper;
use models\lendingwise\tblFileDrawRequests;
use models\lendingwise\db\tblDrawRequestCategories_db;
use models\lendingwise\tblDrawRequestLineItemDocs;
use models\lendingwise\db\tblDrawRequestLineItemDocs_db;
use models\lendingwise\tblDrawRequestCategories;
use models\composite\oDrawManagement\BorrowerDrawCategory;
use models\composite\oDrawManagement\BorrowerDrawLineItem;
use models\composite\oFileDoc\deleteFileDocs;
use models\standard\Dates;
use models\Database2;
use models\lendingwise\tblAutomatedRuleRequestV2;

class DrawRequest extends strongType
{
    use PropertiesMapper;

    public const STATUS_NEW = 'new';
    public const STATUS_PENDING = 'pending';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';
    public ?int $id = null;
    public ?int $LMRId = null;
    public string $status = self::STATUS_NEW;
    public ?int $sowApproved = null;
    public ?int $isDrawRequest = null;
    public ?string $updatedAt = null;
    public ?tblFileDrawRequests $drawRequest = null;
    private ?array $categories = null;

    /**
     * DrawRequest constructor.
     * @param tblFileDrawRequests|null $drawRequest The database draw request object to initialize from.
     */
    public function __construct(?tblFileDrawRequests $drawRequest = null) {
        if ($drawRequest == null) $drawRequest = new tblFileDrawRequests();
        $this->setProperties($drawRequest);
        if ($drawRequest->id) $this->loadCategories();
    }

    /**
     * Saves the current draw request object to the database.
     * @return array The result of the save operation.
     */
    public function save(?array $drawRequestData = null): array {
        if(!empty($drawRequestData)) $this->setFromArray($drawRequestData);
        $saved = $this->drawRequest->save();
        $this->id = $this->drawRequest->id;
        return $saved;
    }

    public function getDbObject(): tblFileDrawRequests {
        return $this->drawRequest;
    }

    /**
     * Sets the properties of the draw request from an associative array.
     * @param array $drawRequestData Associative array containing draw request data.
     * @return void
     */
    private function setFromArray(array $drawRequestData): void {
        $this->drawRequest->id = $drawRequestData['id'] ?? null;
        $this->drawRequest->LMRId = $drawRequestData['LMRId'];
        $this->drawRequest->status = $drawRequestData['status'] ?? self::STATUS_NEW;
        $this->drawRequest->sowApproved = $drawRequestData['sowApproved'] ?? 0;
        $this->drawRequest->isDrawRequest = $drawRequestData['isDrawRequest'] ?? 0;
        $this->setProperties($this->drawRequest);
    }

    /**
     * Delete the draw request from DB
     *
     * @return void
     */
    public function delete(): void {
        if ($this->drawRequest instanceof tblFileDrawRequests) {
            $this->drawRequest->delete();
        }
    }

    /**
     * Converts the draw request object to an associative array.
     * @return array An associative array representation of the draw request.
     */
    public function toArray(): array {
        $data = [];

        $categoriesData = [];
        foreach ($this->categories as $category) {
            $categoriesData[] = $category->toArray();
        }

        return [
            "id" => $this->id,
            "LMRId" => $this->LMRId,
            "status" => $this->status,
            "sowApproved" => $this->sowApproved,
            "isDrawRequest" => $this->isDrawRequest,
            "updatedAt" => $this->updatedAt,
            "categories" => $categoriesData
        ];
    }

    /**
     * Loads categories associated with this draw request from the database.
     * @return void
     */
    public function loadCategories(): void {
        if (!$this->id) {
            return;
        }

        $categoriesData = tblDrawRequestCategories::GetAll(
            [tblDrawRequestCategories_db::COLUMN_DRAWID => $this->id],
            [tblDrawRequestCategories_db::COLUMN_ORDER => 'ASC']
        );

        $this->categories = [];
        foreach ($categoriesData as $categoryData) {
            $this->addCategory(new BorrowerDrawCategory($categoryData));
        }
        $this->sortCategories();
    }

    /**
     * Adds a BorrowerDrawCategory object to the draw request's categories.
     * @param BorrowerDrawCategory $category The category object to add.
     * @return void
     */
    private function addCategory(BorrowerDrawCategory $category): void {
        $this->categories[$category->id] = $category;
    }

    /**
     * Sorts the categories by their order property.
     * @return void
     */
    private function sortCategories(): void {
        uasort($this->categories, function($a, $b) {
            return $a->order <=> $b->order;
        });
    }

    /**
     * Retrieves all categories associated with this draw request.
     * @return array An array of BorrowerDrawCategory objects.
     */
    public function getAllCategories(): array {
        if (is_null($this->categories)) {
            $this->loadCategories();
        }
        return $this->categories ?? [];
    }

    /**
     * Retrieves all line items associated with this draw request.
     * @return array An array of BorrowerDrawLineItem objects.
     */
    public function getAllLineItems(): array {
        $lineItems = [];
        foreach ($this->getAllCategories() as $category) {
            $lineItems = array_merge($lineItems, $category->getAllLineItems());
        }
        return $lineItems;
    }

    /**
     * Update the draw request status based on line item completion.
     * If all line items are completed, set the status to 'approved'.
     * Otherwise, set the status to 'pending'.
     * @return bool True on success, False on failure.
     */

    public function updateDrawRequestStatus(?string $status, ?int $sowApproved = null, ?int $isDrawRequest = null): bool
    {
        if (!$this->drawRequest) {
            return false;
        }

        $requestAllLineItems = $this->getAllLineItems();

        $oldStatus = $this->drawRequest->status;
        $this->drawRequest->status = $status ?? self::STATUS_NEW;
        $this->drawRequest->sowApproved = $sowApproved ?? $this->drawRequest->sowApproved;
        $this->drawRequest->isDrawRequest = $isDrawRequest ?? $this->drawRequest->isDrawRequest;
        if (empty($requestAllLineItems) && $this->drawRequest->status !== self::STATUS_NEW) return false;

        $this->drawRequest->save();

        // Trigger automation rules if status changed
        if ($oldStatus !== $this->drawRequest->status && $this->LMRId) {
            $this->triggerAutomationRules($oldStatus, $this->drawRequest->status);
        }

        return true;
    }

    /**
     * Trigger automation rules for draw request status changes.
     * This method determines the PCID and triggers automation rules.
     *
     * @param string $oldStatus The previous status
     * @param string $newStatus The new status
     */
    private function triggerAutomationRules(string $oldStatus, string $newStatus): void
    {
        if (!$this->LMRId) {
            return;
        }

        // Get PCID and file type from the loan file
        $sql = 'SELECT FPCID, fileType FROM tblFile WHERE LMRId = :LMRId';
        $result = Database2::getInstance()->queryData($sql, ['LMRId' => $this->LMRId]);

        if (!empty($result)) {
            $PCID = $result[0]['FPCID'];
            $fileType = $result[0]['fileType'] ?? 'Loan';

            // Trigger the general automation system
            tblAutomatedRuleRequestV2::Trigger($this->LMRId, $PCID);

            // Also trigger draw management specific automation rules
            $this->triggerDrawManagementAutomationRules($PCID, $fileType, $oldStatus, $newStatus);
        }
    }

    /**
     * Trigger draw management specific automation rules
     *
     * @param int $PCID The processing company ID
     * @param string $fileType The file type
     * @param string $oldStatus The previous status
     * @param string $newStatus The new status
     */
    private function triggerDrawManagementAutomationRules(int $PCID, string $fileType, string $oldStatus, string $newStatus): void
    {
        // The draw management automation rules are now integrated into the main automation system
        // When tblAutomatedRuleRequestV2::Trigger() is called above, it will eventually process
        // any automation rules that match the current draw management status through the
        // enhanced checkAutomatedRulesForLoanFile_orig.php logic

        // No additional action needed here as the integration is complete
    }

    /**
     * Retrieves a specific category by its ID.
     * @param int $categoryId The ID of the category to retrieve.
     * @return BorrowerDrawCategory|null The BorrowerDrawCategory object if found, otherwise null.
     */
    public function getCategoryById($categoryId): ?BorrowerDrawCategory {
        if (is_null($this->categories)) {
            $this->loadCategories();
        }
        return $this->categories[$categoryId] ?? null;
    }

    /**
     * Delete categories by their IDs.
     * @param array $categoryIds Array of category IDs to delete.
     * @return void
     */
    public function deleteCategories(array $categoryIds): void {
        foreach ($categoryIds as $categoryId) {
            $category = $this->getCategoryById($categoryId);
            if ($category) {
                $category->delete();
            }
        }
    }

    /**
     * Delete line items by their IDs.
     * @param array $lineItemIds Array of line item IDs to delete.
     * @return void
     */
    public function deleteLineItems(array $lineItemIds): void {
        $db = Database2::getInstance();
        $db->beginTransaction();
        try {
            foreach ($lineItemIds as $lineItemId) {
                $lineItem = new BorrowerDrawLineItem();
                $lineItem->getDbObject()->id = $lineItemId;
                $this->removeUploadedDocs($lineItemId);
                $lineItem->delete();
            }
            $db->commit();
        }
        catch (\Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }

    /**
     * Remove uploaded documents associated with a line item
     * @param int $lineItemId Line item ID
     * @return void
     */
    private function removeUploadedDocs(int $lineItemId): void {
        $docs = tblDrawRequestLineItemDocs::GetAll(
            [tblDrawRequestLineItemDocs_db::COLUMN_LINEITEMID => $lineItemId,
            tblDrawRequestLineItemDocs_db::COLUMN_ACTIVESTATUS => 1],
        );
        if (empty($docs)) return;
        $ipArray = [
            'docNumber' => 0,
            'userName' => 'drawRequest',
            'userNumber' => 0,
            'userGroup' => 'admin',
            'isSysNotesPrivate' => 1,
            'saveNotes' => 1
        ];

        foreach ($docs as $doc) {
            $ipArray['docNumber'] = $doc->docID;
            deleteFileDocs::getReport($ipArray);
        }
    }
}
