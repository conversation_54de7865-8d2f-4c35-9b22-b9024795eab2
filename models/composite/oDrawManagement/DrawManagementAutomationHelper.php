<?php

namespace models\composite\oDrawManagement;

use models\Database2;
use models\types\strongType;
use models\constants\automationConstants;

/**
 * Helper class to provide draw management data for automation rules
 */
class DrawManagementAutomationHelper extends strongType
{
    /**
     * Get draw management status for automation rule evaluation
     * 
     * @param int $LMRId The loan file ID
     * @return array Array containing draw management status information
     */
    public static function getDrawManagementStatus(int $LMRId): array
    {
        $result = [
            'hasDrawRequest' => false,
            'initialSOWStatus' => null,
            'drawRequestStatus' => null,
            'revisionStatus' => null,
            'currentStatus' => null,
            'isInitialSOW' => false,
            'isDrawRequest' => false,
            'isRevision' => false
        ];
        
        try {
            $drawManager = new DrawRequestManager($LMRId);
            $drawRequest = $drawManager->getDrawRequest();
            
            if (!$drawRequest) {
                return $result;
            }
            
            $result['hasDrawRequest'] = true;
            $result['currentStatus'] = $drawRequest->status;
            
            // Determine the type of draw request
            $isInitialSOW = $drawManager->isInitialScopeOfWork();
            $isDrawRequest = $drawRequest->isDrawRequest;
            
            if ($isInitialSOW) {
                $result['isInitialSOW'] = true;
                $result['initialSOWStatus'] = $drawRequest->status;
            } elseif ($isDrawRequest) {
                $result['isDrawRequest'] = true;
                $result['drawRequestStatus'] = $drawRequest->status;
            } else {
                $result['isRevision'] = true;
                $result['revisionStatus'] = $drawRequest->status;
            }
            
        } catch (\Exception $e) {
            // Log error but don't break automation
            error_log("DrawManagementAutomationHelper error: " . $e->getMessage());
        }
        
        return $result;
    }
    
    /**
     * Check if a draw management automation rule should trigger
     * 
     * @param int $LMRId The loan file ID
     * @param string $ruleType The rule type (InitialSOW, DrawRequest, Revision)
     * @param string $targetStatus The status to check for
     * @return bool True if the rule should trigger
     */
    public static function shouldTriggerRule(int $LMRId, string $ruleType, string $targetStatus): bool
    {
        $drawStatus = self::getDrawManagementStatus($LMRId);
        
        if (!$drawStatus['hasDrawRequest']) {
            return false;
        }
        
        switch ($ruleType) {
            case automationConstants::$automation_InitialSOW:
                return $drawStatus['isInitialSOW'] && $drawStatus['initialSOWStatus'] === $targetStatus;
                
            case automationConstants::$automation_DrawRequest:
                return $drawStatus['isDrawRequest'] && $drawStatus['drawRequestStatus'] === $targetStatus;
                
            case automationConstants::$automation_Revision:
                return $drawStatus['isRevision'] && $drawStatus['revisionStatus'] === $targetStatus;
                
            default:
                return false;
        }
    }
    
    /**
     * Get all draw management rule types
     * 
     * @return array Array of draw management rule types
     */
    public static function getDrawManagementRuleTypes(): array
    {
        return [
            automationConstants::$automation_InitialSOW,
            automationConstants::$automation_DrawRequest,
            automationConstants::$automation_Revision
        ];
    }
    
    /**
     * Check if a rule type is a draw management rule
     * 
     * @param string $ruleType The rule type to check
     * @return bool True if it's a draw management rule type
     */
    public static function isDrawManagementRule(string $ruleType): bool
    {
        return in_array($ruleType, self::getDrawManagementRuleTypes());
    }
}
