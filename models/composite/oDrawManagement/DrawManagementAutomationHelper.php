<?php

namespace models\composite\oDrawManagement;

use models\Database2;
use models\types\strongType;
use models\constants\automationConstants;

/**
 * Helper class to provide draw management data for automation rules
 */
class DrawManagementAutomationHelper extends strongType
{
    /**
     * Get draw management status for automation rule evaluation
     *
     * @param int $LMRId The loan file ID
     * @return array Array containing draw management status information
     */
    public static function getDrawManagementStatus(int $LMRId): array
    {
        $result = [
            'hasDrawRequest' => false,
            'initialSOWStatus' => null,
            'drawRequestStatus' => null,
            'revisionStatus' => null,
            'currentStatus' => null,
            'isInitialSOW' => false,
            'isDrawRequest' => false,
            'isRevision' => false
        ];

        try {
            $drawManager = new DrawRequestManager($LMRId);
            $drawRequest = $drawManager->getDrawRequest();

            if (!$drawRequest) {
                return $result;
            }

            $result['hasDrawRequest'] = true;
            $result['currentStatus'] = $drawRequest->status;

            // Determine the type of draw request
            $isInitialSOW = $drawManager->isInitialScopeOfWork();
            $isDrawRequest = $drawRequest->isDrawRequest;

            if ($isInitialSOW) {
                $result['isInitialSOW'] = true;
                $result['initialSOWStatus'] = $drawRequest->status;
            } elseif ($isDrawRequest) {
                $result['isDrawRequest'] = true;
                $result['drawRequestStatus'] = $drawRequest->status;
            } else {
                $result['isRevision'] = true;
                $result['revisionStatus'] = $drawRequest->status;
            }

        } catch (\Exception $e) {
            error_log("DrawManagementAutomationHelper error: " . $e->getMessage());
        }

        return $result;
    }

}
