<?php
namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\cypher;
use models\lendingwise\tblFile;
use models\lendingwise\tblFileDrawRequests;
use models\lendingwise\db\tblFileDrawRequests_db;
use models\lendingwise\tblDrawRequests_h;
use models\lendingwise\db\tblDrawRequests_h_db;
use models\lendingwise\tblDrawRequestLineItems_h;
use models\lendingwise\db\tblDrawRequestLineItems_h_db;
use models\composite\oDrawManagement\DrawRequest;
use models\composite\oDrawManagement\BorrowerDrawCategory;
use models\composite\oDrawManagement\BorrowerDrawLineItem;
use models\composite\oDrawManagement\SowTemplateManager;
use models\composite\oDrawManagement\DrawRequestsHistory;
use models\composite\oDrawManagement\traits\DrawRequestHistoryHandler;
use models\composite\oDrawManagement\DrawSummaryCalculatedValues;
use models\lendingwise\tblProcessingCompanyDrawTemplateSettings;
use models\lendingwise\tblProcessingCompany;
use models\Controllers\backoffice\LMRequest;
use models\Database2;

class DrawRequestManager extends strongType
{
    use DrawRequestHistoryHandler;

    private ?int $LMRId = null;
    private ?int $drawRequestId = null;
    private ?DrawRequest $drawRequest = null;
    public const MAX_CATEGORIES = 20;

    /**
     * DrawRequestManager constructor.
     * @param int $LMRId The LMR ID to initialize the manager with.
     */
    public function __construct($LMRId)
    {
        $this->LMRId = $LMRId;
        $this->loadDrawRequest();
    }

    /**
     * Static factory method to create an instance of DrawRequestManager.
     * @param int|null $LMRId The LMR ID to initialize the manager with.
     * @return DrawRequestManager A new instance of DrawRequestManager.
     */
    public static function forLoanFile($LMRId = null): self {
        if ($LMRId === null) {
            throw new \InvalidArgumentException("LMRId must be provided or available in PageVariables.");
        }
        if(!is_numeric($LMRId)) {
            $LMRId = cypher::myDecryption($LMRId);
        }
        return new self($LMRId);
    }

    /**
     * Load or create draw request for the current LMR ID.
     * @return void
     */
    private function loadDrawRequest(): void
    {
        if (!$this->LMRId) {
            return;
        }

        $drawRequestData = tblFileDrawRequests::Get([tblFileDrawRequests_db::COLUMN_LMRID => $this->LMRId]);
        if ($drawRequestData) {
            $this->drawRequest = new DrawRequest($drawRequestData);
            $this->drawRequestId = $this->drawRequest->id;
        } else {
            $this->drawRequest = $this->getOrCreateDrawRequest();
        }
    }

    /**
     * Get or create a draw request for the current LMR ID.
     * @return DrawRequest The draw request object.
     */
    public function getOrCreateDrawRequest(): DrawRequest
    {
        if (is_null($this->drawRequest)) {
            $this->drawRequest = new DrawRequest();
            $drawRequestData = [
                'LMRId' => $this->LMRId,
                'status' => DrawRequest::STATUS_NEW
            ];
            $this->drawRequest->save($drawRequestData);
            $this->drawRequestId = $this->drawRequest->id;
            $this->copyProcessingCompanyTemplateData();
            $this->updateDrawSummaryCalculatedValues();
        }
        return $this->drawRequest;
    }

    /**
     * Public getter for drawRequestId.
     * @return int|null The draw request ID.
     */
    public function getDrawRequestId(): ?int
    {
        return $this->drawRequestId;
    }

    /**
     * Retrieves the DrawRequest object.
     * @return DrawRequest|null The borrower draw request object.
     */
    public function getDrawRequest(): ?DrawRequest
    {
        return $this->drawRequest;
    }

    /**
     * Check if a draw request exists for the current LMR ID.
     * @return bool True if draw request exists, false otherwise.
     */
    public function hasDrawRequest(): bool
    {
        return !is_null($this->drawRequest);
    }

    /**
     * Retrieves the draw request data as an array.
     * If no borrower draw request exists, returns empty array.
     * If borrower draw request exists, returns draw request data.
     * @return array An array containing the draw request data.
     */
    public function getDrawRequestDataArray(): array
    {
        if ($this->hasDrawRequest()) {
            return $this->drawRequest->toArray();
        }

        return [];
    }

    /**
     * Copy processing company template data to the borrower draw request for a new request.
     * This function copies template categories and line items to borrower tables,
     * resetting IDs for categories and using new category IDs for line items.
     * @return void
     */
    private function copyProcessingCompanyTemplateData(): void
    {
        if (!$this->drawRequest || !$this->drawRequest->id) {
            return;
        }

        $templateData = $this->getTemplateDataArray();

        if (empty($templateData['categories'])) {
            return;
        }

        $db = Database2::getInstance();
        $db->beginTransaction();

        try {
            $this->copyTemplateCategoriesAndLineItems($templateData['categories']);
            $db->commit();
            $this->loadDrawRequest();
        } catch (\Exception $e) {
            if($this->hasDrawRequest()) $this->drawRequest->delete();
            $db->rollBack();
            throw $e;
        }
    }

    /**
     * Copy template categories and line items to borrower tables.
     * @param array $templateCategoriesData Array of template categories with line items.
     * @return void
     */
    private function copyTemplateCategoriesAndLineItems(array $templateCategoriesData): void
    {
        foreach ($templateCategoriesData as $templateCategory) {
            $borrowerCategory = new BorrowerDrawCategory();

            $categoryData = [
                'id' => null,
                'drawId' => $this->drawRequest->id,
                'categoryName' => $templateCategory['categoryName'],
                'description' => $templateCategory['description'] ?? '',
                'order' => $templateCategory['order']
            ];

            $borrowerCategory->save($categoryData);
            $newCategoryId = $borrowerCategory->id;

            if (!empty($templateCategory['lineItems'])) {
                $this->copyTemplateLineItems($templateCategory['lineItems'], $newCategoryId);
            }
        }
    }

    /**
     * Copy template line items to borrower line items table.
     * @param array $templateLineItems Array of template line items.
     * @param int $newCategoryId The new category ID to associate line items with.
     * @return void
     */
    private function copyTemplateLineItems(array $templateLineItems, int $newCategoryId): void
    {
        foreach ($templateLineItems as $templateLineItem) {
            $borrowerLineItem = new BorrowerDrawLineItem();

            $lineItemData = [
                'id' => null,
                'drawId' => $this->drawRequest->id,
                'categoryId' => $newCategoryId,
                'name' => $templateLineItem['name'],
                'description' => $templateLineItem['description'] ?? '',
                'order' => $templateLineItem['order'],
                'cost' => 0.00,
                'completedAmount' => 0.00,
                'completedPercent' => 0.00,
                'notes' => ''
            ];

            $borrowerLineItem->save($lineItemData);
        }
    }

    /**
     * Get categories data from template (fallback when no borrower draw request exists).
     * @return array An array containing the template categories data.
     */
    private function getTemplateDataArray(): array
    {
        try {
            $PCID = self::PCIDFromLMRId($this->LMRId);
            if (!$PCID) {
                return [];
            }

            $sowTemplateManager = SowTemplateManager::forProcessingCompany($PCID);
            return $sowTemplateManager->getTemplateDataArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Save categories with transaction handling and validation.
     * This method processes and saves categories, ensuring they are associated with the correct draw request.
     * It also handles the deletion of categories that are no longer present in the provided data.
     * @param array $categoriesData Array of category data.
     * @return bool True on success, False on failure.
     * @throws \Exception On invalid category IDs or database errors.
     */
    public function saveCategories(array $categoriesData): bool
    {
        if (count($categoriesData) > self::MAX_CATEGORIES) {
            return false;
        }

        $db = Database2::getInstance();
        $db->beginTransaction();
        try {
            $drawRequest = $this->getOrCreateDrawRequest();
            $existingCategories = $drawRequest->getAllCategories();
            $existingCategoryIds = array_column($existingCategories, 'id');
            $updatedCategoryIds = [];

            foreach ($categoriesData as $catData) {
                $id = $catData['id'];
                $catData['drawId'] = $drawRequest->id;

                if ($id) {
                    $categoryObject = $this->getCategoryById($id);
                } else {
                    $catData['id'] = null;
                    $categoryObject = new BorrowerDrawCategory();
                }
                $categoryObject->save($catData);
                $updatedCategoryIds[] = $categoryObject->id;
            }

            $idsToDelete = array_diff($existingCategoryIds, $updatedCategoryIds);
            $this->deleteCategories($idsToDelete);

            $db->commit();
            $this->loadDrawRequest();
            return true;

        } catch (\Exception $e) {
            $db->rollBack();
            return false;
        }
    }

    /**
     * Save line items with transaction handling and validation.
     * This method processes and saves line items, ensuring they are associated with the correct categories and draw request.
     * It also handles the deletion of line items that are no longer present in the provided data.
     * @param array $lineItemsData Grouped line items data format: `['categoryId' => [['id', 'categoryName', 'cost', ...], ...]]`
     * @return bool True on success, False on failure.
     * @throws \Exception On invalid category IDs or database errors.
     */
    public function saveLineItems(array $lineItemsData, bool $isDraft = false): bool
    {
        $db = Database2::getInstance();
        $db->beginTransaction();
        try {
            if (count($lineItemsData) > self::MAX_CATEGORIES) {
                return false;
            }

            $drawRequest = $this->getOrCreateDrawRequest();
            $liDataArray = [];

            foreach ($lineItemsData as $categoryId => $lineItems) {
                $borrowerCategory = $this->getCategoryById($categoryId);
                $existingLineItemIds = $borrowerCategory ? array_column($borrowerCategory->getAllLineItems(), 'id') : [];
                $updatedLineItemIds = [];

                foreach ($lineItems as $liData) {
                    $liDataArray[] = $liData;
                    $id = $liData['id'] ?? null;
                    $categoryId = $liData['categoryId'];
                    $liData['drawId'] = $drawRequest->id;
                    if ($id) {
                        $lineItemObject = $borrowerCategory->getLineItemById($id);
                    } else {
                        $liData['id'] = null;
                        $lineItemObject = new BorrowerDrawLineItem();
                    }
                    $lineItemObject->save($liData);
                    $updatedLineItemIds[] = $lineItemObject->id;
                }

                $idsToDelete = array_diff($existingLineItemIds, $updatedLineItemIds);
                $this->drawRequest->deleteLineItems($idsToDelete);
            }

            $this->saveScopeOfWorkData($isDraft);
            if (!$isDraft) $this->handleDrawRequestHistory(DrawRequest::STATUS_PENDING, $liDataArray);

            $this->loadDrawRequest();

            $db->commit();
            return true;

        } catch (\Exception $e) {
            $db->rollBack();
            return false;
        }
    }

    /**
     * Update the draw summary calculated values for the current draw request.
     * This method creates a new DrawSummaryCalculatedValues instance and updates the calculated values.
     * @return void
     */
    private function updateDrawSummaryCalculatedValues(): void
    {
        $drawSummaryCalculatedValues = new DrawSummaryCalculatedValues($this->LMRId, $this->drawRequest->id);
        $drawSummaryCalculatedValues->updateValues();
    }

    /**
     * Submit the scope of work for the draw request.
     * @param bool $isDraft Whether the submission is a draft.
     * @return bool True on success, False on failure.
     */

    public function saveScopeOfWorkData(bool $isDraft): bool
    {
        $status = DrawRequest::STATUS_PENDING;
        if ($isDraft) {
            $status = $this->drawRequest->status;
        }
        $sowApproved = 0;
        $isDrawRequest = 0;
        return $this->drawRequest->updateDrawRequestStatus($status, $sowApproved, $isDrawRequest);
    }

    /**
     * Get a category by its ID.
     * @param int $categoryId The category ID.
     * @return BorrowerDrawCategory|null The category object or null if not found.
     */
    private function getCategoryById(int $categoryId): ?BorrowerDrawCategory
    {
        if (!$this->drawRequest) {
            return null;
        }

        $categories = $this->drawRequest->getAllCategories();
        foreach ($categories as $category) {
            if ($category->id == $categoryId) {
                return $category;
            }
        }
        return null;
    }

    /**
     * Get a line item by its ID.
     * @param int $lineItemId The line item ID.
     * @return BorrowerDrawLineItem|null The line item object or null if not found.
     */
    protected function getLineItemById(int $lineItemId): ?BorrowerDrawLineItem
    {
        if (!$this->drawRequest) {
            return null;
        }

        $allLineItems = $this->drawRequest->getAllLineItems();
        foreach ($allLineItems as $lineItem) {
            if ($lineItem->id == $lineItemId) {
                return $lineItem;
            }
        }
        return null;
    }

    /**
     * Get draw request history records for the current draw request.
     * @return array Array of draw request history data.
     *
     * @param bool $toArray Whether to return the data as an array or objects.
     */
    public function getDrawRequestHistory(?bool $toArray = false): array
    {
        if (!$this->drawRequest || !$this->drawRequest->id) {
            return [];
        }

        return DrawRequestsHistory::getHistoryByDrawId($this->drawRequest->id, $toArray);
    }

    /**
     * Save draw request data including status updates and line item modifications.
     * Handles status changes, line item updates, and creates history records.
     * @param array $postData The post data containing status and lineItems information.
     * @return bool True on success, False on failure.
     */
    public function saveDrawRequestData(array $postData): bool
    {
        if (!$this->drawRequest || empty($postData['lineItems'])) {
            return false;
        }

        $db = Database2::getInstance();
        $db->beginTransaction();
        try {
            $status = $postData['status'];
            $sowApproved = $this->drawRequest->sowApproved;
            $isDrawRequest = $this->drawRequest->sowApproved;

            if ($status === DrawRequest::STATUS_APPROVED) {
                $sowApproved = 1;
            }

            $this->drawRequest->updateDrawRequestStatus($status, $sowApproved, $isDrawRequest);

            if (isset($postData['lineItems']) && is_array($postData['lineItems'])) {
                foreach ($postData['lineItems'] as $lineItemId => $lineItemData) {
                    $lineItem = $this->getLineItemById((int)$lineItemId);

                    $lineItemDbObj = $lineItem->getDbObject();
                    $lineItemDbObj->completedAmount = $this->getLineItemCompletedAmount($lineItem, $lineItemData, $status);
                    $lineItemDbObj->completedPercent = $this->getLineItemCompletedPercent($lineItem, $lineItemData, $status);
                    $lineItemDbObj->requestedAmount = $this->getLineItemRequestedAmount($lineItem, $lineItemData, $status);
                    $lineItemDbObj->disbursedAmount = $this->getLineItemDisbursedAmount($lineItem, $lineItemData, $status);
                    $lineItemDbObj->notes = !empty($lineItemData['notes']) ? $lineItemData['notes'] : $lineItemDbObj->notes;
                    $lineItemDbObj->lenderNotes = !empty($lineItemData['lenderNotes']) ? $lineItemData['lenderNotes'] : $lineItemDbObj->lenderNotes;
                    $lineItemDbObj->rejectReason = !empty($lineItemData['rejectReason']) ? $lineItemData['rejectReason'] : $lineItemDbObj->rejectReason;
                    if ($status === DrawRequest::STATUS_APPROVED) {
                        $lineItemDbObj->rejectReason = '';
                    }
                    $lineItemDbObj->save();
                }
            }

            $this->handleDrawRequestHistory($status, $postData['lineItems'], 1);
            if ($status === DrawRequest::STATUS_APPROVED) $this->updateDrawSummaryCalculatedValues();

            $db->commit();
            $this->loadDrawRequest();
            return true;
        } catch (\Exception $e) {
            $db->rollBack();
            return false;
        }
    }

    /**
     * Cancel a Scope of Work (SOW) revision request.
     * Resets the draw request status to approved and restores line item values from the last approved SOW.
     * @return bool True on success, False on failure.
     */
    public function cancelSowRevisionRequest(): bool
    {
        if (!$this->drawRequest || $this->drawRequest->status !== DrawRequest::STATUS_PENDING) {
            return false;
        }

        $db = Database2::getInstance();
        $db->beginTransaction();
        try {
            //Reset draw request status
            $this->drawRequest->updateDrawRequestStatus(DrawRequest::STATUS_APPROVED, 1, 0);
            $this->deleteHistoryRecords(DrawRequestsHistory::STATUS_PENDING, 0);

            //Reset line item cost, completed amounts, percentage to last approved SOW
            $lastApprovedSow = tblDrawRequests_h::GetAll([
                tblDrawRequests_h_db::COLUMN_DRAWID => $this->drawRequest->id,
                tblDrawRequests_h_db::COLUMN_STATUS => DrawRequestsHistory::STATUS_APPROVED,
                tblDrawRequests_h_db::COLUMN_ISDRAWREQUEST => 0],
                [tblDrawRequests_h_db::COLUMN_SUBMITTEDAT => 'DESC'], 1);
            $lastApprovedSow = reset($lastApprovedSow);
            $prvSowLineItems = tblDrawRequestLineItems_h::GetAll([
                tblDrawRequestLineItems_h_db::COLUMN_RECORDID => $lastApprovedSow->id
            ]);

            $lineItems = $this->drawRequest->getAllLineItems();
            foreach ($lineItems as $lineItem) {
                $lineItemHistory = array_filter($prvSowLineItems, function($li) use ($lineItem) {
                    return $li->lineItemId === $lineItem->id;
                });
                $lineItemHistory = reset($lineItemHistory);

                if ($lineItemHistory instanceof tblDrawRequestLineItems_h) {
                    $dbObj = $lineItem->getDbObject();
                    $dbObj->cost = $lineItemHistory->cost;
                    $dbObj->completedAmount = $lineItemHistory->completedAmount;
                    $dbObj->completedPercent = $lineItemHistory->completedPercent;
                    $dbObj->save();
                }
            }

            $db->commit();
            $this->loadDrawRequest();
            return true;
        } catch (\Exception $e) {
            $db->rollBack();
            return false;
        }
    }

    /**
     * Cancel the draw request.
     * @return bool True on success, False on failure.
     */

    public function cancelDrawRequest(): bool
    {
        if (!$this->drawRequest || $this->drawRequest->status !== DrawRequest::STATUS_PENDING) {
            return false;
        }

        $db = Database2::getInstance();
        $db->beginTransaction();
        try {
            //Reset draw request status
            $this->drawRequest->updateDrawRequestStatus(DrawRequest::STATUS_APPROVED);
            //Reset line item requested amounts
            $lineItems = $this->drawRequest->getAllLineItems();
            foreach ($lineItems as $lineItem) {
                $lineItem->getDbObject()->requestedAmount = 0;
                $lineItem->getDbObject()->save();
            }

            $this->deleteHistoryRecords(DrawRequestsHistory::STATUS_PENDING, 1);

            $db->commit();
            $this->loadDrawRequest();
            return true;
        } catch (\Exception $e) {
            $db->rollBack();
            return false;
        }
    }

    /**
     * Delete history records for a specific status and isDrawRequest flag.
     * @param string $status The status of the history records to delete.
     * @param int $isDrawRequest The isDrawRequest flag of the history records to delete.
     *
     * @return void
     */

    private function deleteHistoryRecords(string $status, int $isDrawRequest): void
    {
        // Delete line items history associated with the latest history record
        $latestHistory = tblDrawRequests_h::Get([
            tblDrawRequests_h_db::COLUMN_DRAWID => $this->drawRequest->id,
            tblDrawRequests_h_db::COLUMN_ISDRAWREQUEST => $isDrawRequest,
            tblDrawRequests_h_db::COLUMN_STATUS => $status
        ]);

        $latestHistoryLineItems = tblDrawRequestLineItems_h::GetAll([
            tblDrawRequestLineItems_h_db::COLUMN_RECORDID => $latestHistory->id
        ]);
        foreach ($latestHistoryLineItems as $lineItem) {
            $lineItem->Delete();
        }

        // Delete latest history record
        $latestHistory->Delete();
    }

    /**
     * Get draw request line items history data for a specific history record.
     * Combines data from main line items table with history table data.
     * @param int $recordId The history record ID.
     * @return array Array of categories with their line items history data.
     */
    public function getDrawRequestLineItemsHistory($recordId): array
    {
        if (!$this->drawRequest || !$this->drawRequest->id) {
            return [];
        }

        $lineItemsHistory = tblDrawRequestLineItems_h::GetAll([
            tblDrawRequestLineItems_h_db::COLUMN_RECORDID => $recordId
        ]);

        if (empty($lineItemsHistory)) {
            return [];
        }

        $categories = $this->drawRequest->getAllCategories();
        $categoriesData = [];

        foreach ($categories as $category) {
            $categoryData = [
                'id' => $category->id,
                'categoryName' => $category->categoryName,
                'description' => $category->description,
                'order' => $category->order,
                'lineItems' => []
            ];

            $categoryLineItems = $category->getAllLineItems();

            foreach ($categoryLineItems as $lineItem) {
                $historyData = null;
                foreach ($lineItemsHistory as $historyItem) {
                    if ($historyItem->lineItemId == $lineItem->id) {
                        $historyData = $historyItem;
                        break;
                    }
                }

                if ($historyData) {
                    $lineItemData = [
                        'id' => $lineItem->id,
                        'name' => $lineItem->name,
                        'description' => $lineItem->description,
                        'order' => $lineItem->order,
                        'cost' => $historyData->cost,
                        'completedAmount' => $historyData->completedAmount,
                        'completedPercent' => $historyData->completedPercent,
                        'requestedAmount' => $historyData->requestedAmount,
                        'disbursedAmount' => $historyData->disbursedAmount,
                        'notes' => $historyData->notes,
                        'lenderNotes' => $historyData->lenderNotes
                    ];

                    $categoryData['lineItems'][] = $lineItemData;
                }
            }

            if (!empty($categoryData['lineItems'])) {
                usort($categoryData['lineItems'], function($a, $b) {
                    return $a['order'] <=> $b['order'];
                });

                $categoriesData[] = $categoryData;
            }
        }

        usort($categoriesData, function($a, $b) {
            return $a['order'] <=> $b['order'];
        });

        return $categoriesData;
    }

    /**
     * Get the completed amount for a line item based on its status.
     * @param BorrowerDrawLineItem $lineItem The line item object.
     * @param array $lineItemData The line item data.
     * @return float The completed amount.
     */

    protected function getLineItemCompletedAmount(BorrowerDrawLineItem $lineItem, $lineItemData, $status): float
    {
        if($this->drawRequest->isDrawRequest && $status === DrawRequest::STATUS_APPROVED) {
            $completedAmount = $lineItem->completedAmount + $lineItemData['requestedAmount'];
        } else {
            $completedAmount = $lineItem->completedAmount;
        }
        return $completedAmount;
    }

    /**
     * Get the completed percentage for a line item based on its status.
     * @param BorrowerDrawLineItem $lineItem The line item object.
     * @param array $lineItemData The line item data.
     * @return float The completed percentage.
     */

    protected function getLineItemCompletedPercent(BorrowerDrawLineItem $lineItem, $lineItemData, $status): float
    {
        $completedAmount = $this->getLineItemCompletedAmount($lineItem, $lineItemData, $status);
        $cost = $lineItem->cost;
        return $cost > 0 ? ($completedAmount / $cost) * 100 : 0;
    }

    /**
     * Get the requested amount for a line item based on the draw request status.
     * @param BorrowerDrawLineItem $lineItem The line item object.
     * @param array $lineItemData The line item data.
     * @param string $status The current status of the draw request.
     * @return float The requested amount.
     */
    protected function getLineItemRequestedAmount(BorrowerDrawLineItem $lineItem, $lineItemData, $status): float
    {
        if($this->drawRequest->sowApproved && $status === DrawRequest::STATUS_PENDING) {
            $requestedAmount = $lineItemData['requestedAmount'];
        } else {
            $requestedAmount = $lineItem->requestedAmount;
        }
        return $requestedAmount;
    }

    /**
     * Get the disbursed amount for a line item based on the draw request status.
     * @param BorrowerDrawLineItem $lineItem The line item object.
     * @param array $lineItemData The line item data.
     * @param string $status The current status of the draw request.
     * @return float The disbursed amount.
     */
    protected function getLineItemDisbursedAmount(BorrowerDrawLineItem $lineItem, $lineItemData, $status): float
    {
        if($this->drawRequest->sowApproved && $status === DrawRequest::STATUS_APPROVED) {
            $disbursedAmount = $lineItem->disbursedAmount + $lineItemData['requestedAmount'];
        } else {
            $disbursedAmount = $lineItem->disbursedAmount;
        }
        return $disbursedAmount;
    }

    /**
     * Get the approved amount for a line item based on the draw request status.
     * @param array $lineItemData The line item data.
     * @param string $status The current status of the draw request.
     * @return float The approved amount.
     */
    protected function getLineItemApprovedAmount($lineItemData, $status): float
    {
        if($this->drawRequest->sowApproved && $status === DrawRequest::STATUS_APPROVED) {
            $approvedAmount = $lineItemData['requestedAmount'];
        } else {
            $approvedAmount = 0;
        }
        return $approvedAmount;
    }

    /**
     * Delete categories by their IDs.
     * @param array $categoryIds Array of category IDs to delete.
     * @return void
     */
    private function deleteCategories(array $categoryIds): void
    {
        foreach ($categoryIds as $categoryId) {
            $category = $this->getCategoryById($categoryId);
            if ($category) {
                $category->delete();
            }
        }
    }

    /**
     * Get PCID from LMRId.
     * @param int $LMRId The LMR ID.
     * @return int|null The PCID or null if not found.
     */
    public static function PCIDFromLMRId(int $LMRId): ?int
    {
        $fileData = tblFile::Get(['LMRId' => $LMRId]);
        return $fileData ? $fileData->FPCID : null;
    }

    /**
     * Get the count of draw requests for the current draw.
     * Returns the number of approved draw requests plus one for the current request.
     * @return int The total count of draw requests.
     */
    public function getDrawRequestsCount(): int
    {
        $reqNum = tblDrawRequests_h::GetCount(['drawId' => $this->drawRequest->id, 'status' => DrawRequestsHistory::STATUS_APPROVED, 'isDrawRequest' => 1]);
        return ($reqNum ?? 0) + 1;
    }

    /**
     * Check if this is the initial scope of work (no previous approved SOW exists).
     * @return bool True if this is the initial scope of work, false otherwise.
     */
    public function isInitialScopeOfWork(): bool
    {
        return $this->getInitialScopeOfWork() && $this->getInitialScopeOfWork()->id === $this->drawRequest->id;
    }

    /**
     * Get the initial scope of work record for the current draw request.
     * @return tblDrawRequests_h|null The initial scope of work record or null if not found.
     */
    public function getInitialScopeOfWork(): ?tblDrawRequests_h
    {
        return tblDrawRequests_h::Get(['drawId' => $this->drawRequest->id, 'isDrawRequest' => 0]);
    }

    /**
     * Check if the file has draw management enabled based on various conditions.
     * Checks processing company settings, draw template settings, and property rehabilitation needs.
     * @param int $LMRId The LMR ID to check.
     * @return bool True if the file has draw management enabled, false otherwise.
     */
    public static function fileHasDrawManagement(int $LMRId): bool
    {
        LMRequest::setLMRId($LMRId);
        $PCID = LMRequest::File()->FPCID;
        $drawSettings = tblProcessingCompanyDrawTemplateSettings::Get(['PCID' => $PCID]);
        $templateSettings = tblProcessingCompany::Get(['PCID' => $PCID]);
        $needsRehab = LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->propertyNeedRehab === 'Yes';
        if (!$templateSettings || !$drawSettings) return false;

        return $templateSettings->drawManagement && $templateSettings->enableDrawManagementV2 && $needsRehab && !$drawSettings->enableSimpleMode;
    }

    /**
     * Get the view mode for the draw management system based on the file settings.
     * Returns 'normal' if the file has draw management enabled, 'simple' otherwise.
     * @param int $LMRId The LMR ID to check.
     * @return string The view mode ('normal' or 'simple').
     */
    public static function getViewMode(int $LMRId): string
    {
        if (self::fileHasDrawManagement($LMRId)) {
            return 'normal';
        }
        return 'simple';
    }

}
