<?php

namespace models\composite\oAutomatedRules;

use models\Database2;
use models\Resque\jobStatusCode;
use models\types\strongType;



/**
 *
 */
class saveAutomatedRules extends strongType
{
    /**
     * @param $PCID
     * @param $id
     * @param $action
     * @param $eventType
     * @return void
     */
    private static function deleteRecord($PCID, $id, $action, $eventType)
    {
        $sql = '
            DELETE FROM
                tblAutomatedRulesEvents
            WHERE
                PCID = :PCID
                AND tblARId = :tblARId
                AND action = :action
                AND eventType = :eventType
       ;';

        Database2::getInstance()->executeQuery($sql, [
            'PCID' => $PCID
            , 'tblARId' => $id
            , 'action' => $action
            , 'eventType' => $eventType
        ]);
    }

    /**
     * @param $PCID
     * @param $tblARId
     * @param $actionId
     * @param $action
     * @param $eventType
     * @param $createdBy
     * @return void
     */
    private static function insertRecord(
        $PCID,
        $tblARId,
        $actionId,
        $action,
        $eventType,
        $createdBy
    ): void
    {
        $sql = '
INSERT INTO tblAutomatedRulesEvents
    (PCID, tblARId, actionId, action, eventType, eventStatus, createdBy)
VALUES
    (
     :PCID
     , :tblARId
     , :actionId
     , :action
     , :eventType
     , :eventStatus
     , :createdBy
     )
    ';
        Database2::getInstance()->update($sql, [
            'PCID' => $PCID
            , 'tblARId' => $tblARId
            , 'actionId' => $actionId
            , 'action' => $action
            , 'eventType' => $eventType
            , 'eventStatus' => 1
            , 'createdBy' => $createdBy
        ]);
    }

    /**
     * @return string[]
     */
    private static function getInsertConditions($noOfDayStatus, $statusUpdateId): array
    {
        //insert conditions
        $ruleFor = $_POST['ruleFor'] ?? [];
        $conditionType = $_POST['conditionType'] ?? [];
        $isIsNotArray = $_POST['isIsNot'] ?? [];

        // Debug: Log the POST data for draw management rules
        if (in_array('InitialSOW', $ruleFor) || in_array('DrawRequest', $ruleFor) || in_array('Revision', $ruleFor)) {
            error_log('Draw Management Rule Debug - ruleFor: ' . print_r($ruleFor, true));
            error_log('Draw Management Rule Debug - POST parameterOptions_1: ' . print_r($_POST['parameterOptions_1'] ?? 'NOT SET', true));
            error_log('Draw Management Rule Debug - POST parameterOptions_2: ' . print_r($_POST['parameterOptions_2'] ?? 'NOT SET', true));
            error_log('Draw Management Rule Debug - POST parameterOptions_3: ' . print_r($_POST['parameterOptions_3'] ?? 'NOT SET', true));
        }

        $rqc = $rqv = $rqs = '';
        for ($i = 0; $i < count($ruleFor); $i++) {
            $a = $i + 1;
            $rule = $ruleFor[$i];
            $condType = $conditionType[$i];
            $isIsNot = $isIsNotArray[$i];
            //Rule For
            $rqc .= $rqs . 'ruleFor' . $a;
            $rqv .= $rqs . " '" . trim($rule) . "' ";
            $rqs = ', ';
            //IS-IS NOT
            $rqc .= $rqs . 'isIsNot' . $a;
            $rqv .= $rqs . " '" . trim($isIsNot) . "' ";

            if (isset($_POST['parameterOptions_' . $a])) {
                $param = implode('~', $_POST['parameterOptions_' . $a]);
                $rqc .= $rqs . 'params' . $a;
                $rqv .= $rqs . " '" . trim($param) . "' ";
            }
            //no of days, change status repeatRule
            if ($a == 1) {
                if ($noOfDayStatus) {
                    $rqc .= $rqs . 'noOfDayStatus' . $a;
                    $rqv .= $rqs . " '" . trim($noOfDayStatus) . "' ";
                }
                //logic updated sc-62775
                //if we have no of days then status update is based on that
                //else status update is instant and the statusUpdateId is saved in the events table
                if ($noOfDayStatus && $statusUpdateId) {
                    $rqc .= $rqs . 'changeStatus' . $a;
                    $rqv .= $rqs . " '" . trim($statusUpdateId) . "' ";
                }
            }
            if ($i < 2) { // max 2 conditions
                $rqc .= $rqs . 'conditionType' . $a;
                $rqv .= $rqs . " '" . trim($condType) . "' ";
            }
        }
        return [
            'rqc' => $rqc,
            'rqv' => $rqv
        ];
    }

    /**
     * @param $PCID
     * @param $id
     * @param $date
     * @return void
     */
    private static function doRuleCondUpdated($PCID, $id, $date)
    {
        //UnQueue from the resque
        $sql = '
                SELECT t1.id
                    ,  t1.tblARId
                    , t1.actionId
                    , t1.actionName
                    ,  t1.jobId
                FROM tblAutomatedRuleActionLoanFile t1
                LEFT JOIN tblAutomatedRulesEvents t2 ON t2.PCID = t2.PCID
                WHERE
                    t1.jobId IS NOT NULL
                    AND t1.jobId != \'\'
                    AND t1.PCID = :PCID
                    AND t1.tblARId = :id
                    AND t1.jobStatusCode IN (1,2)
                    AND t2.contEventStatus = 0
';
        $sqlParams = [
            'PCID' => $PCID,
            'id' => $id,
        ];

        $resultJobEnqueue = Database2::getInstance()->queryData($sql, $sqlParams);

        if (!sizeof($resultJobEnqueue)) {
            return;
        }

        //Update status in the table
        $id = [];
        foreach ($resultJobEnqueue as $ids) {
            $id[] = intval($ids['id']);
        }
        $newIds = implode(',', $id);
        $sql = '
UPDATE
    tblAutomatedRuleActionLoanFile
SET jobStatusCode = :jobStatusCode
  , jobStatus = :jobStatus
  , response = :response
  , updatedOn = :updatedOn
   WHERE id IN (' . $newIds . ')
';
        //echo $delJobEnqueueQry;exit();
        Database2::getInstance()->executeQuery($sql, [
            'response' => 'Rule was updated, so this scheduled action got cancelled.',
            'updatedOn' => $date,
            'jobStatus' => jobStatusCode::STATUS_CANCELLED,
            'jobStatusCode' => jobStatusCode::STATUS_CANCELLED_CODE,
        ]);
    }

    /**
     * @param $PCID
     * @param $RowId
     * @param $userNumber
     * @param $action
     * @param $schaction
     * @param $tasksId
     * @param $schtasksId
     * @param $emailsId
     * @param $schemailsId
     * @param $webhooksId
     * @param $schwebhooksId
     * @param $contEventStatusTask
     * @param $contEventStatusEmail
     * @param $contEventStatusWebhook
     * @param $employeesId
     * @param $statusUpdateId
     * @return void
     */
    private static function insertActions(
        $PCID,
        $RowId,
        $userNumber,
        $action,
        $schaction,
        $tasksId,
        $schtasksId,
        $emailsId,
        $schemailsId,
        $webhooksId,
        $schwebhooksId,
        $contEventStatusTask,
        $contEventStatusEmail,
        $contEventStatusWebhook,
        $employeesId,
        $statusUpdateId
    )
    {
        if (!$action && !$schaction) {
            return;
        }

        //Insert Action(s)
        //task/email/webhook / //Schedule - task/email/webhook
        if ($tasksId != '') { //Task
            $taskIds = explode(',', $tasksId);
            foreach ($taskIds as $tid) {
                self::insertRecord($PCID, $RowId, $tid, 'Task', 'Instant', $userNumber);
            }
        }
        if ($schtasksId != '') { //Schedule Task
            $schtaskIds = explode(',', $schtasksId);
            foreach ($schtaskIds as $schtid) {
                self::insertRecord($PCID, $RowId, $schtid, 'Task', 'Schedule', $userNumber);
            }
        }
        if ($emailsId != '') { //Email
            $emailIds = explode(',', $emailsId);
            foreach ($emailIds as $eid) {
                self::insertRecord($PCID, $RowId, $eid, 'Email', 'Instant', $userNumber);
            }
        }
        if ($schemailsId != '') { //Schedule Email
            $schemailIds = explode(',', $schemailsId);
            foreach ($schemailIds as $scheid) {
                self::insertRecord($PCID, $RowId, $scheid, 'Email', 'Schedule', $userNumber);
            }
        }
        if ($webhooksId != '') { //Webhook
            $webhookIds = explode(',', $webhooksId);
            foreach ($webhookIds as $wid) {
                self::insertRecord($PCID, $RowId, $wid, 'Webhook', 'Instant', $userNumber);
            }
        }
        if ($schwebhooksId != '') { //Schedule Webhook
            $schwebhookIds = explode(',', $schwebhooksId);
            foreach ($schwebhookIds as $schwid) {
                self::insertRecord($PCID, $RowId, $schwid, 'Webhook', 'Schedule', $userNumber);
            }
        }

        if($employeesId != '') {
            $employeeIds = explode(',', $employeesId);
            foreach ($employeeIds as $eid) {
                self::insertRecord($PCID, $RowId, $eid, 'Employee', 'Instant', $userNumber);
            }
        }

        if ($statusUpdateId) {
            self::insertRecord($PCID, $RowId, $statusUpdateId, 'Change File Status', 'Instant', $userNumber);
        }

        //Continue Events Execution - Task, Email & Webhooks.
        if ($contEventStatusTask) {
            $sql = "
UPDATE tblAutomatedRulesEvents
SET contEventStatus = 1
WHERE PCID = :PCID
AND tblARId = :tblARId
 AND action = 'Task'
 AND eventType = 'Schedule'
 AND eventStatus = 1
 AND rowStatus = 1
 AND actionId IN ($contEventStatusTask);
 ";
            Database2::getInstance()->executeQuery($sql, [
                'PCID' => $PCID,
                'tblARId' => $RowId,
            ]);
        }
        if ($contEventStatusEmail) {
            $sql = "
UPDATE tblAutomatedRulesEvents
SET contEventStatus = 1
WHERE PCID = :PCID
AND tblARId = :tblARId
 AND action = 'Email'
 AND eventType = 'Schedule'
 AND eventStatus = 1
 AND rowStatus = 1
 AND actionId IN ($contEventStatusEmail);
 ";
            Database2::getInstance()->executeQuery($sql, [
                'PCID' => $PCID,
                'tblARId' => $RowId,
            ]);
        }
        if ($contEventStatusWebhook) {
            $sql = "
UPDATE tblAutomatedRulesEvents
SET contEventStatus = 1
WHERE PCID = :PCID
AND tblARId = :tblARId
 AND action = 'Webhook'
 AND eventType = 'Schedule'
 AND eventStatus = 1
 AND rowStatus = 1
 AND actionId IN ($contEventStatusWebhook);
 ";
            Database2::getInstance()->executeQuery($sql, [
                'PCID' => $PCID,
                'tblARId' => $RowId,
            ]);
        }
    }

    /**
     * @param $qc
     * @param $params
     * @param $userGroup
     * @param $userNumber
     * @param $userName
     * @param $PCID
     * @param $insertConditions
     * @param $date
     * @return int
     */
    private static function doInsert(
        $qc,
        $params,
        $userGroup,
        $userNumber,
        $userName,
        $PCID,
        $insertConditions,
        $date
    ): int
    {
        $sql = '
INSERT INTO
    tblAutomatedRules
    (PCID, ' . implode(',', $qc) . ', userGroup, userNumber, userName, createdBy, createdOn)
    VALUES (
        :PCID
        , :' . implode(', :', $qc) . '
        , :userGroup
        , :userNumber
        , :userName
        , :userNumber
        , :createdOn
     )
     ';
        $params['userGroup'] = $userGroup;
        $params['userNumber'] = $userNumber;
        $params['userName'] = $userName;
        $params['createdOn'] = $date;
        $params['PCID'] = $PCID;


        $RowId = Database2::getInstance()->insert($sql, $params);

        $rqc = $insertConditions['rqc'];
        $rqv = $insertConditions['rqv'];

        $sql = '
INSERT INTO
    tblAutomatedRulesConditions
    (tblARId, createdBy, ' . $rqc . ')
VALUES (
        :tblARId
        , :createdBy
        , ' . $rqv . '
        );';

        Database2::getInstance()->executeQuery($sql, [
            'tblARId' => $RowId,
            'createdBy' => $userNumber,
        ]);
        return $RowId;
    }

    /**
     * @param $action
     * @param $schaction
     * @param $tasksId
     * @param $PCID
     * @param $id
     * @param $userNumber
     * @param $schtasksId
     * @param $emailsId
     * @param $schemailsId
     * @param $webhooksId
     * @param $schwebhooksId
     * @param $contEventStatusTask
     * @param $contEventStatusEmail
     * @param $contEventStatusWebhook
     * @param $employeesId
     * @param $statusUpdateId
     * @return void
     */
    private static function deleteInsertActions(
        $action,
        $schaction,
        $tasksId,
        $PCID,
        $id,
        $userNumber,
        $schtasksId,
        $emailsId,
        $schemailsId,
        $webhooksId,
        $schwebhooksId,
        $contEventStatusTask,
        $contEventStatusEmail,
        $contEventStatusWebhook,
        $employeesId,
        $statusUpdateId
    )
    {
        if (!$action && !$schaction) {
            return;
        }
        //Insert Action(s)
        //Instant -  task/email/webhook/assign employee/change status
        //Schedule - task/email/webhook

        self::deleteRecord($PCID, $id, 'Task', 'Instant');

        if ($tasksId) { //Task
            $taskIds = explode(',', $tasksId);
            foreach ($taskIds as $tid) {
                self::insertRecord($PCID, $id, $tid, 'Task', 'Instant', $userNumber);
            }
        }

        self::deleteRecord($PCID, $id, 'Task', 'Schedule');

        if ($schtasksId) { //Schedule Task
            $schtaskIds = explode(',', $schtasksId);
            foreach ($schtaskIds as $schtid) {
                self::insertRecord($PCID, $id, $schtid, 'Task', 'Schedule', $userNumber);
            }
        }

        self::deleteRecord($PCID, $id, 'Email', 'Instant');

        if ($emailsId) { //Email
            $emailIds = explode(',', $emailsId);
            foreach ($emailIds as $eid) {
                self::insertRecord($PCID, $id, $eid, 'Email', 'Instant', $userNumber);
            }
        }

        self::deleteRecord($PCID, $id, 'Email', 'Schedule');

        if ($schemailsId) { //Schedule Email
            $schemailIds = explode(',', $schemailsId);
            foreach ($schemailIds as $scheid) {
                self::insertRecord($PCID, $id, $scheid, 'Email', 'Schedule', $userNumber);
            }
        }

        self::deleteRecord($PCID, $id, 'Webhook', 'Instant');

        if ($webhooksId) { //Webhook
            $webhookIds = explode(',', $webhooksId);
            foreach ($webhookIds as $wid) {
                self::insertRecord($PCID, $id, $wid, 'Webhook', 'Instant', $userNumber);
            }
        }

        self::deleteRecord($PCID, $id, 'Webhook', 'Schedule');

        if ($schwebhooksId) { //Schedule Webhook
            $schwebhookIds = explode(',', $schwebhooksId);
            foreach ($schwebhookIds as $schwid) {
                self::insertRecord($PCID, $id, $schwid, 'Webhook', 'Schedule', $userNumber);
            }
        }

        self::deleteRecord($PCID, $id, 'Employee', 'Instant');

        if($employeesId) {
            $employeeIds = explode(',', $employeesId);
            foreach ($employeeIds as $eid) {
                self::insertRecord($PCID, $id, $eid, 'Employee', 'Instant', $userNumber);
            }
        }

        self::deleteRecord($PCID, $id, 'Change File Status', 'Instant');

        if ($statusUpdateId) {
            self::insertRecord($PCID, $id, $statusUpdateId, 'Change File Status', 'Instant', $userNumber);
        }

        //Continue Events Execution - Task, Email & Webhooks.
        if ($contEventStatusTask) {
            $sql = '
UPDATE tblAutomatedRulesEvents
SET contEventStatus = 1
WHERE PCID = :PCID
AND tblARId = :tblARId
AND action = :action
AND eventType = :eventType
AND eventStatus = :eventStatus
AND rowStatus = :rowStatus
AND actionId IN (' . $contEventStatusTask . ')
';
            Database2::getInstance()->executeQuery($sql, [
                'PCID' => $PCID,
                'tblARId' => $id,
                'action' => 'Task',
                'eventType' => 'Schedule',
                'eventStatus' => 1,
                'rowStatus' => 1,
            ]);
        }

        if ($contEventStatusEmail) {
            $sql = '
UPDATE tblAutomatedRulesEvents
SET contEventStatus = 1
WHERE PCID = :PCID
AND tblARId = :tblARId
AND action = :action
AND eventType = :eventType
AND eventStatus = :eventStatus
AND rowStatus = :rowStatus
AND actionId IN (' . $contEventStatusEmail . ')
';
            Database2::getInstance()->executeQuery($sql, [
                'PCID' => $PCID,
                'tblARId' => $id,
                'action' => 'Email',
                'eventType' => 'Schedule',
                'eventStatus' => 1,
                'rowStatus' => 1,
            ]);
        }

        if ($contEventStatusWebhook) {
            $sql = "UPDATE tblAutomatedRulesEvents
SET contEventStatus = 1
WHERE PCID = :PCID
AND tblARId = :tblARId
AND action = :action
AND eventType = :eventType
AND eventStatus = :eventStatus
AND rowStatus = :rowStatus
AND actionId IN ($contEventStatusWebhook)
";
            Database2::getInstance()->executeQuery($sql, [
                'PCID' => $PCID,
                'tblARId' => $id,
                'action' => 'Webhook',
                'eventType' => 'Schedule',
                'eventStatus' => 1,
                'rowStatus' => 1,
            ]);
        }
    }

    /**
     * @param array $qu
     * @param array $params
     * @param array $insertConditions
     * @param int $id
     * @param $userNumber
     * @param $date
     * @return int
     */
    private static function doUpdate(
        array $qu,
        array $params,
        array $insertConditions,
        int $id,
        $userNumber,
        $date
    ): int
    {
        //check if something to update
        $sql = '
UPDATE
    tblAutomatedRules
SET updatedOn = :updatedOn
    , ' . implode(',', $qu) . '
WHERE id = :id
';
        Database2::getInstance()->update($sql, $params);

        $rqc = $insertConditions['rqc'];
        $rqv = $insertConditions['rqv'];

        //Delete the existing data
        $sql = '
    DELETE FROM tblAutomatedRulesConditions
    WHERE tblARId = :tblARId
    ';
        Database2::getInstance()->executeQuery($sql, [
            'tblARId' => $id,
        ]);


        $sql = '
INSERT INTO
    tblAutomatedRulesConditions
    (tblARId, createdBy, updatedOn, ' . $rqc . ')
VALUES
    (
     :tblARId
     , :createdBy
     , :updatedOn
     , ' . $rqv . '
    )
    ';

        //Insert New Conditions
        Database2::getInstance()->executeQuery($sql, [
            'tblARId' => $id,
            'createdBy' => $userNumber,
            'updatedOn' => $date,
        ]);

        return $id;

    }

    /**
     * @param $params
     * @return int
     */
    public static function getReport($params): ?int
    {
        $userGroup = $params['userGroup'] ?? '';
        $userNumber = $params['userNumber'] ?? '';
        $userName = $params['userName'] ?? '';

        $id = $params['id'] ?? '';
        $isRuleCondUpdated = $params['isRuleCondUpdated'] ?? '';
        $PCID = $params['PCID'] ?? '';
        $fileType = $params['fileType'] ?? '';
        $ruleName = $params['ruleName'] ?? '';
        $ruleDescription = $params['ruleDescription'] ?? '';
        $repeatRule = $params['repeatRule'] ?? '';

        $action = $params['action'] ?? '';
        $tasksId = $params['tasksId'] ?? '';
        $emailsId = $params['emailsId'] ?? '';
        $webhooksId = $params['webhooksId'] ?? '';

        $schaction = $params['schaction'] ?? '';
        $schtasksId = $params['schtasksId'] ?? '';
        $schemailsId = $params['schemailsId'] ?? '';
        $schwebhooksId = $params['schwebhooksId'] ?? '';

        $contEventStatusTask = implode(',', $_POST['contEventStatusTask'] ?? []);
        $contEventStatusEmail = implode(',', $_POST['contEventStatusEmail'] ?? []);
        $contEventStatusWebhook = implode(',', $_POST['contEventStatusWebhook'] ?? []);

        $statusUpdateId = $params['statusUpdateId'] ?? '';
        $noOfDayStatus = $params['noOfDayStatus'] ?? 0;
        $employeesId = $params['employeesId'] ?? '';

        $date = $params['dateTimeNow'];

        $qc = [];
        $qu = [];

        $params = [
            'updatedOn' => $date,
            'id' => $id,
        ];

        if (isset($fileType)) {
            $qc [] = 'fileType';
            $qu [] = 'fileType = :fileType';
            $params['fileType'] = trim($fileType);
        }

        if (isset($ruleName)) {
            $qc [] = 'ruleName';
            $qu [] = 'ruleName = :ruleName';
            $params['ruleName'] = trim($ruleName);
        }

        if (isset($ruleDescription)) {
            $qc [] = 'ruleDescription';
            $qu [] = 'ruleDescription = :ruleDescription';
            $params['ruleDescription'] = trim($ruleDescription);
        }

        if (isset($repeatRule)) {
            $qc [] = 'repeatRule';
            $qu [] = 'repeatRule = :repeatRule';
            $params['repeatRule'] = trim($repeatRule);
        }

        if (!sizeof($qc)) {
            return null;
        }

        $insertConditions = self::getInsertConditions($noOfDayStatus, $statusUpdateId);

        if ($id > 0) { // update
            $RowId = self::doUpdate(
                $qu,
                $params,
                $insertConditions,
                $id,
                $userNumber,
                $date
            );
            self::deleteInsertActions(
                $action,
                $schaction,
                $tasksId,
                $PCID,
                $id,
                $userNumber,
                $schtasksId,
                $emailsId,
                $schemailsId,
                $webhooksId,
                $schwebhooksId,
                $contEventStatusTask,
                $contEventStatusEmail,
                $contEventStatusWebhook,
                $employeesId,
                $statusUpdateId
            );


            //Delete the jobs that are enqueue for this rule
            //except those which are mandatory. i.e. with contEventStatus = 1
            if ($isRuleCondUpdated == 'Yes') {
                self::doRuleCondUpdated($PCID, $id, $date);
            }

        } else { //check if something to add

            $RowId = self::doInsert(
                $qc,
                $params,
                $userGroup,
                $userNumber,
                $userName,
                $PCID,
                $insertConditions,
                $date
            );

            self::insertActions(
                $PCID,
                $RowId,
                $userNumber,
                $action,
                $schaction,
                $tasksId,
                $schtasksId,
                $emailsId,
                $schemailsId,
                $webhooksId,
                $schwebhooksId,
                $contEventStatusTask,
                $contEventStatusEmail,
                $contEventStatusWebhook,
                $employeesId,
                $statusUpdateId
            );
        }

        return $RowId;
    }
}
