<?php

namespace models\composite\oAutomatedRules;

use models\Database2;
use models\types\strongType;
use models\composite\oDrawManagement\DrawRequestManager;
use models\constants\automationConstants;

/**
 * Class to check and trigger automated rules for draw management status changes
 */
class checkAutomatedRulesForDrawManagement extends strongType
{
    /**
     * Check and return automation rules that match the current draw request status
     *
     * @param int $LMRId The loan file ID
     * @param int $PCID The processing company ID
     * @param string $fileType The file type/module
     * @return array Array of matching automation rules
     */
    public static function getReport(int $LMRId, int $PCID, string $fileType): array
    {
        $drawManager = new DrawRequestManager($LMRId);
        $drawRequest = $drawManager->getDrawRequest();

        if (!$drawRequest) {
            return [];
        }

        $currentStatus = $drawRequest->status;
        $isInitialSOW = $drawManager->isInitialScopeOfWork();
        $isDrawRequest = $drawRequest->isDrawRequest;

        $ruleType = self::determineRuleType($isInitialSOW, $isDrawRequest);

        if (!$ruleType) {
            return [];
        }

        return self::getMatchingRules($PCID, $fileType, $ruleType, $currentStatus);
    }

    /**
     * Determine the rule type based on draw request characteristics
     *
     * @param bool $isInitialSOW Whether this is the initial scope of work
     * @param int $isDrawRequest Whether this is a draw request (1) or SOW (0)
     * @return string|null The rule type constant or null if not applicable
     */
    private static function determineRuleType(bool $isInitialSOW, int $isDrawRequest): ?string
    {
        if ($isInitialSOW) {
            return automationConstants::$automation_InitialSOW;
        } elseif ($isDrawRequest) {
            return automationConstants::$automation_DrawRequest;
        } else {
            return automationConstants::$automation_Revision;
        }
    }

    /**
     * Get automation rules that match the specified criteria
     *
     * @param int $PCID The processing company ID
     * @param string $fileType The file type/module
     * @param string $ruleType The rule type (InitialSOW, DrawRequest, Revision)
     * @param string $currentStatus The current draw request status
     * @return array Array of matching automation rules
     */
    private static function getMatchingRules(int $PCID, string $fileType, string $ruleType, string $currentStatus): array
    {
        $sql = "
            SELECT ar.id as ruleId, ar.*
            FROM tblAutomatedRules ar
            WHERE ar.PCID = :PCID
            AND ar.fileType = :fileType
            AND ar.ruleStatus = 1
            AND ar.rowStatus = 1
        ";

        $rules = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
            'fileType' => $fileType
        ]);

        if (empty($rules)) {
            return [];
        }

        $matchingRules = [];

        foreach ($rules as $rule) {
            if (self::ruleMatchesDrawManagement($rule['id'], $ruleType, $currentStatus)) {
                $matchingRules[] = $rule;
            }
        }

        return $matchingRules;
    }

    /**
     * Check if a specific rule matches the draw management criteria
     *
     * @param int $ruleId The automation rule ID
     * @param string $ruleType The rule type (InitialSOW, DrawRequest, Revision)
     * @param string $currentStatus The current draw request status
     * @return bool True if the rule matches, false otherwise
     */
    private static function ruleMatchesDrawManagement(int $ruleId, string $ruleType, string $currentStatus): bool
    {
        $sql = "
            SELECT *
            FROM tblAutomatedRulesConditions
            WHERE tblARId = :ruleId
            AND status = 1
        ";

        $conditions = Database2::getInstance()->queryData($sql, ['ruleId' => $ruleId]);

        if (empty($conditions)) {
            return false;
        }

        foreach ($conditions as $condition) {
            $matches = true;

            for ($i = 1; $i <= 3; $i++) {
                $ruleForField = "ruleFor{$i}";
                $paramsField = "params{$i}";

                if (!empty($condition[$ruleForField])) {
                    $conditionRuleFor = $condition[$ruleForField];
                    $conditionParams = $condition[$paramsField];

                    if ($conditionRuleFor === $ruleType) {
                        $allowedStatuses = explode('~', trim($conditionParams, '~'));
                        if (!in_array($currentStatus, $allowedStatuses)) {
                            $matches = false;
                            break;
                        }
                    } else {
                        $matches = false;
                        break;
                    }
                }
            }

            if ($matches) {
                return true;
            }
        }

        return false;
    }
}
