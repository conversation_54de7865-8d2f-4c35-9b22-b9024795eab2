<?php

use models\composite\oAutomatedRules\getAutomatedRule;
use models\composite\oAutomatedRules\getAutomatedRuleCFS;
use models\composite\oAutomatedRules\getAutomatedRuleConditions;
use models\composite\oAutomatedRules\getAutomatedRuleIAE;
use models\composite\oAutomatedRules\getAutomatedRuleIAEmp;
use models\composite\oAutomatedRules\getAutomatedRuleIAT;
use models\composite\oAutomatedRules\getAutomatedRuleIAW;
use models\composite\oAutomatedRules\getAutomatedRuleInstantEmails;
use models\composite\oAutomatedRules\getAutomatedRuleInstantTasks;
use models\composite\oAutomatedRules\getAutomatedRuleInstantWebhook;
use models\composite\oAutomatedRules\getAutomatedRuleSAE;
use models\composite\oAutomatedRules\getAutomatedRuleSAT;
use models\composite\oAutomatedRules\getAutomatedRuleSAW;
use models\composite\oAutomatedRules\getAutomatedRuleScheduleEmails;
use models\composite\oAutomatedRules\getAutomatedRuleScheduleTasks;
use models\composite\oAutomatedRules\getAutomatedRuleScheduleWebhook;
use models\composite\oBroker\listAllAgents;
use models\composite\oPC\getPCServiceType;
use models\composite\oPrimaryStatus\getPCPrimaryStatus;
use models\composite\oSubstatus\getPCFileSubstatus;
use models\composite\oWorkflow\getPCWFSteps;
use models\constants\automationConstants;
use models\constants\borrowerStatusArray;
use models\constants\drawRequestStatusArray;
use models\constants\gl\glBorrowerType;
use models\constants\gl\glEventReferralDateArray;
use models\cypher;
use models\lendingwise\tblBranch;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;

//session_start();
//global variables
global $assignedPCID, $PCID, $userGroup, $userName, $modulesArray, $fileModules;
//load models
//variables
$id = '';
$pcid = '';
//Rule Details
$getRuleDetails = [];
$ruleStatus = '';
$fileType = '';
$ruleName = '';
$ruleDescription = '';

$repeatRule = 0;
//Rule Condition Details
$getRuleCondDetails = [];
$ruleCondCnt = 0;
$ruleFor = '';
$conditionType = '';
$params = '';
//Instant Action(s)
$getRuleInstantTasks = [];
$getRuleInstantEmails = [];
$getRuleInstantWebhooks = [];
$action = '';
$TasksId = '';
$EmailsId = '';
$WebhooksId = '';
$UsersId = '';
$changeFileStatus = '';

//Schedule Action(s)
$getRuleScheduleTasks = [];
$getRuleScheduleEmails = [];
$getRuleScheduleWebhooks = [];

$schaction = '';
$schTasksId = '';
$schEmailsId = '';
$schWebhooksId = '';

$showActionDiv = 0;

//Instant Action(s)
$Tasks = [];
$Emails = [];
$Webhooks = [];
//Schedule Action(s)
$ScheduleTasks = [];
$ScheduleEmails = [];
$ScheduleWebhooks = [];


$pageTitle = 'Create New Automated Rule';
if (isset($_REQUEST['id'])) $id = cypher::myDecryption(Request::GetClean('id'));

$glEventReferralDateArray = array_merge(
    glEventReferralDateArray::$glEventReferralDateArray,
    glEventReferralDateArray::$glGeneralDates,
    glEventReferralDateArray::$glHMLODates,
    glEventReferralDateArray::$glLMSSDates,
    glEventReferralDateArray::$glBusinessFundingDates
);
$glEventReferralDateAllFileTypeArray = array_merge(
    glEventReferralDateArray::$glEventReferralDateArray,
    glEventReferralDateArray::$glGeneralDates
);
$glEventReferralDateAllFileTypeArrayKeys = array_keys($glEventReferralDateAllFileTypeArray);

//update the rule status
if ($id > 0) {
    $pageTitle = 'Update Automated Rule';

    $params = ['PCID' => $assignedPCID, 'id' => $id];
    $getRuleDetails = getAutomatedRule::getReport($params);

    $ruleStatus = $getRuleDetails['ruleStatus'];
    $fileType = $getRuleDetails['fileType'];
    $ruleName = $getRuleDetails['ruleName'];
    $ruleDescription = $getRuleDetails['ruleDescription'];
    $repeatRule = $getRuleDetails['repeatRule'];

    $getRuleCondDetails = getAutomatedRuleConditions::getReport($id);
    if (count($getRuleCondDetails) > 0) {
        $ruleCondCnt = count($getRuleCondDetails);
    }

    $getRuleInstantTasks = getAutomatedRuleInstantTasks::getReport($params);
    if (count($getRuleInstantTasks)) {
        $ids = [];
        foreach ($getRuleInstantTasks as $aid) {
            $ids[] = $aid['actionId'];
        }
        $TasksId = implode(',', $ids);
        $showActionDiv = 1;
    }

    $getRuleScheduleTasks = getAutomatedRuleScheduleTasks::getReport($params);
    if (count($getRuleScheduleTasks)) {
        $ids = [];
        foreach ($getRuleScheduleTasks as $aid) {
            $ids[] = $aid['actionId'];
        }
        $schTasksId = implode(',', $ids);
        //$showActionDiv = 1;
    }
    $getRuleInstantEmails = getAutomatedRuleInstantEmails::getReport($params);
    if (count($getRuleInstantEmails)) {
        $ids = [];
        foreach ($getRuleInstantEmails as $aid) {
            $ids[] = $aid['actionId'];
        }
        $EmailsId = implode(',', $ids);
        $showActionDiv = 1;
    }
    $getRuleScheduleEmails = getAutomatedRuleScheduleEmails::getReport($params);
    if (count($getRuleScheduleEmails)) {
        $ids = [];
        foreach ($getRuleScheduleEmails as $aid) {
            $ids[] = $aid['actionId'];
        }
        $schEmailsId = implode(',', $ids);
        $showActionDiv = 1;
    }
    $getRuleInstantWebhooks = getAutomatedRuleInstantWebhook::getReport($params);
    if (count($getRuleInstantWebhooks)) {
        $ids = [];
        foreach ($getRuleInstantWebhooks as $aid) {
            $ids[] = $aid['actionId'];
        }
        $WebhooksId = implode(',', $ids);
        $showActionDiv = 1;
    }
    $getRuleScheduleWebhooks = getAutomatedRuleScheduleWebhook::getReport($params);
    if (count($getRuleScheduleWebhooks)) {
        $ids = [];
        foreach ($getRuleScheduleWebhooks as $aid) {
            $ids[] = $aid['actionId'];
        }
        $schWebhooksId = implode(',', $ids);
        $showActionDiv = 1;
    }
    //Instant Action(s)
    $Tasks = getAutomatedRuleIAT::getReport($params);
    $Emails = getAutomatedRuleIAE::getReport($params);
    $Webhooks = getAutomatedRuleIAW::getReport($params);

    //Schedule Action(s)
    $ScheduleTasks = getAutomatedRuleSAT::getReport($params);
    $ScheduleEmails = getAutomatedRuleSAE::getReport($params);
    $ScheduleWebhooks = getAutomatedRuleSAW::getReport($params);

    //Assign Employee(s)
    $Employees = getAutomatedRuleIAEmp::getReport($params);
    if (count($Employees) > 0) {
        $ids = [];
        foreach ($Employees as $eid) {
            $ids[] = $eid->actionId;
        }
        $UsersId = implode(',', $ids);
    }
    //Change File Status
    $changeFileStatus = getAutomatedRuleCFS::getReport($params);
}
//fix for old reference date values
//based on the pc file modules
$RDFT = '';
if (in_array('HMLO', $fileModules)) {
    $RDFT = 'HMLO-';
}
if (in_array('LM', $fileModules) || in_array('SS', $fileModules)) {
    $RDFT = 'LMSS-';
}
if (in_array('loc', $fileModules)) {
    $RDFT = 'LOC-';
}
//add tool tip
$pageTitle .= ' <i class="tooltipAjax fas fa-info-circle text-primary"
data-html="true"
data-toggle="tooltip"
data-placement="right"
title=""
data-original-title="When you want to automatically trigger an action during the creation of a file, please include a rule trigger for when a Loan File is Created."></i>';
?>
<div class="card card-custom">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo $pageTitle; ?>
            </h3>
        </div>
        <div class="card-toolbar">
            <?php
            if ($id != '' && $id > 0) {
                $switchClass = '';
                $switchChecked = 'switch-success';
                if ($ruleStatus == 0) {
                    $switchClass = 'switch-success';
                    $switchChecked = '';
                }
                if ($ruleStatus == 1) {
                    $switchClass = 'switch-success';
                    $switchChecked = "checked='checked'";
                }
                ?>
                <label class="font-weight-bold h4 col-form-label">OFF &nbsp; &nbsp;</label>
                <span class="switch switch-outline switch-icon <?php echo $switchClass; ?>">
                <label>
                    <input type="checkbox" class="updateStatus" id="<?php echo $PCID . '~' . htmlspecialchars($id); ?>"
                           value="<?php echo htmlspecialchars($ruleStatus); ?>"  <?php echo $switchChecked; ?> name="status">
                    <span></span>
                </label>
            </span>
                <label class="font-weight-bold h4 col-form-label">&nbsp; &nbsp; ON &nbsp; &nbsp;</label>
            <?php } ?>
            <a href="<?php echo CONST_URL_BOSSL; ?>automatedActions.php?pcId=<?php echo cypher::myEncryption($assignedPCID); ?>&userGroup=<?php echo $userGroup; ?>&tabNumb=4&view=list"
               class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2" id="back"
               data-placement="top" title="" data-original-title="Back to Rules List">
                <i class="ki ki-arrow-back icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body m-0">
        <form name="automatedRulesForm" id="automatedRulesForm" class="form-horizontal"
              action="<?php echo CONST_URL_BOSSL; ?>automatedRulesController.php" method="post"
              enctype="multipart/form-data">
            <input type="hidden" id="id" name="id" value="<?php echo htmlspecialchars($id); ?>">
            <input type="hidden" id="bosslurl" name="bosslurl" value="<?php echo CONST_URL_BOSSL; ?>">
            <input type="hidden" id="PCID" name="PCID" value="<?php echo $assignedPCID; ?>">
            <input type="hidden" id="userGroup" name="userGroup" value="<?php echo $userGroup; ?>">
            <input type="hidden" id="userNumber" name="userNumber" value="<?php echo PageVariables::$userNumber; ?>">
            <input type="hidden" id="userName" name="userName" value="<?php echo $userName; ?>">
            <input type="hidden" id="isRuleCondUpdated" name="isRuleCondUpdated" value="">
            <!--// Instant Action(s) //-->
            <input type="hidden" id="action" name="event" value="">
            <input type="hidden" id="tasksId" name="tasksId" value="<?php echo $TasksId; ?>">
            <input type="hidden" id="emailsId" name="emailsId" value="<?php echo $EmailsId; ?>">
            <input type="hidden" id="webhooksId" name="webhooksId" value="<?php echo $WebhooksId; ?>">
            <input type="hidden" id="usersId" name="usersId" value="<?php echo $UsersId;?>">
            <!--// Schedule Action(s) //-->
            <input type="hidden" id="schaction" name="schevent" value="">
            <input type="hidden" id="schtasksId" name="schtasksId" value="<?php echo $schTasksId; ?>">
            <input type="hidden" id="schemailsId" name="schemailsId" value="<?php echo $schEmailsId; ?>">
            <input type="hidden" id="schwebhooksId" name="schwebhooksId" value="<?php echo $schWebhooksId; ?>">
            <!--// Schedule Action(s) Continue Event(s) //-->
            <input type="hidden" id="schTsCe" value="">
            <input type="hidden" id="schEmCe" value="">
            <input type="hidden" id="schWhCe" value="">


            <div class="col-md-12">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label for="ruleFileType" class="col-md-5 font-weight-bold">Select File Type / Module </label>
                        <div class="col-md-7">
                            <select name="ruleFileType" id="ruleFileType" class="form-control input-sm">
                                <option value="">-Select-</option>
                                <?php
                                foreach ($modulesArray as $pcModule) {
                                    ?>
                                    <option value="<?php echo $pcModule['moduleCode']; ?>" <?php if ($pcModule['moduleCode'] == $fileType) echo 'selected=selected'; ?> ><?php echo $pcModule['moduleName']; ?></option>
                                    <?php
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label for="ruleName" class="col-md-5 font-weight-bold">Rule Name</label>
                        <div class="col-md-7">
                            <input type="text" name="ruleName" id="ruleName" class="form-control"
                                   value="<?php echo htmlspecialchars($ruleName); ?>" autocomplete="off">
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label for="ruleName" class="col-md-5 font-weight-bold">Rule Description</label>
                        <div class="col-md-7">
                            <textarea name="ruleDescription" id="ruleDescription"
                                      class="form-control input-sm"><?php echo htmlspecialchars($ruleDescription); ?></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label for="repeatRule" class="col-md-5 font-weight-bold">
                                Allow rule to repeat? <i class="popoverClass fas fa-info-circle text-primary"
                                                         data-html="true" data-placement="bottom"
                                                         data-content="If this rule is bind with the No of Days then actions in this rule won't repeat."></i>
                            </label>
                            <div class="col-md-7">
                            <span class="switch switch-icon">
                                <label>
                                    <input class="switch-on" type="checkbox" value="<?php echo $repeatRule; ?>"
                                           id="repeatRuleSwitch" <?php if ($repeatRule) echo 'checked=checked'; ?>
                                           onchange="toggleSwitch('repeatRuleSwitch', 'repeatRule','1','0' );">
                                    <input type="hidden" name="repeatRule" id="repeatRule"
                                           value="<?php echo $repeatRule; ?>">
                                    <span></span>
                                </label>
                            </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="clearfix"></div>
            <?php if ($ruleCondCnt == 0) { // add rule?>
                <input type="hidden" id="statusUpdateId" name="statusUpdateId" value="">
                <div class="col-md-12 pl-0 hidden" id="conditions">
                    <div class="card card-custom alert alert-custom alert-default">
                        <div class="col-md-12 pl-0 row parameterDivs" id="newParameterDiv_1">
                            <div class="col-md-6 hidden" id="parameter_1">
                                <div class="form-group row">
                                    <label for="ruleFor_1" class="col-md-5 font-weight-bold">Rule triggered when
                                        <i class="popoverClass fas fa-info-circle text-primary"
                                           data-html="true" data-placement="bottom"
                                           data-content="<?php echo automationConstants::$automation_rule_trigger_tooltip; ?>"></i>
                                    </label>
                                    <div class="col-md-7">
                                        <select name="ruleFor[]" id="ruleFor_1" class="ruleFor form-control input-sm">
                                            <option value="">-Select-</option>
                                            <option value="<?php echo automationConstants::$automation_FCU; ?>">
                                                <?php echo automationConstants::$automation_LoanFileStatusTitle; ?>
                                            </option>
                                            <option value="<?php echo automationConstants::$automation_PFS; ?>">
                                                <?php echo automationConstants::$automation_PrimaryFileStatus; ?>
                                            </option>
                                            <option value="<?php echo automationConstants::$automation_FSS; ?>">
                                                <?php echo automationConstants::$automation_FileSubStatus; ?>
                                            </option>
                                            <option value="<?php echo automationConstants::$automation_Workflow; ?>">
                                                <?php echo automationConstants::$automation_Workflow; ?>
                                            </option>
                                            <option value="<?php echo automationConstants::$automation_Branch; ?>">
                                                <?php echo automationConstants::$automation_Branch; ?>
                                            </option>
                                            <option value="<?php echo automationConstants::$automation_Broker; ?>">
                                                <?php echo automationConstants::$automation_Broker; ?>
                                            </option>
                                            <option value="<?php echo automationConstants::$automation_LO; ?>">
                                                <?php echo automationConstants::$automation_LoanOfficer; ?>
                                            </option>
                                            <option value="<?php echo automationConstants::$automation_BorrowerStatus; ?>">
                                                <?php echo automationConstants::$automation_BorrowerStatus_Title; ?>
                                            </option>
                                            <option value="<?php echo automationConstants::$automation_BorrowerType; ?>">
                                                <?php echo automationConstants::$automation_BorrowerType_Title; ?>
                                            </option>
                                            <option value="<?php echo automationConstants::$automation_LoanProgram; ?>">
                                                <?php echo automationConstants::$automation_LoanProgram_Title; ?>
                                            </option>
                                            <option value="<?php echo automationConstants::$automation_InitialSOW; ?>">
                                                <?php echo automationConstants::$automation_InitialSOW_Title; ?>
                                            </option>
                                            <option value="<?php echo automationConstants::$automation_DrawRequest; ?>">
                                                <?php echo automationConstants::$automation_DrawRequest_Title; ?>
                                            </option>
                                            <option value="<?php echo automationConstants::$automation_Revision; ?>">
                                                <?php echo automationConstants::$automation_Revision_Title; ?>
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6 hidden andOr" id="andOr_1">
                                    <div class="col-md-6 text-center">
                                        <input type="hidden" name="conditionType[]" id="conditionType_1" class=""
                                               value="">
                                        <label><h2 class="is andOrTxt" id="andOrTxt_1"></h2></label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 row">
                                <div class="col-md-1 hidden" id="isDiv_1">
                                    <div class="form-group row">
                                        <input type="hidden" name="isIsNot[]" id="isIsNot_1" class="isIsNotVal"
                                               value="">
                                        <label class="col-form-label"><h6 class="is isIsNot" id="isIsNotId_1">IS</h6>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-11">
                                    <div class="form-group row">
                                        <div class="col-md-6 hidden" id="parameterValue_1">
                                            <select name="parameterOptions_1[]" id="parameterOptions_1"
                                                    style="width:280px" class="parameterValue js-example-basic-multiple"
                                                    multiple="multiple">

                                            </select>
                                        </div>
                                        <div class="col-md-5 hidden noOfDayStatusDiv" id="noOfDayStatusDiv_1">
                                            <div class="row">
                                                <div class="input-group">
                                                    <span class="input-group-text">For</span>
                                                    <input type="text" name="noOfDayStatus" id="noOfDayStatus"
                                                           maxlength="3" class="form-control" autocomplete="off"
                                                           value="">
                                                    <span class="input-group-text">
                                                        Day(s) &nbsp;
                                                        <i class="popoverClass fas fa-info-circle text-primary"
                                                           data-html="true" data-placement="bottom"
                                                           data-content="This field can be left as 0 to carry out the expected action as soon as the conditions are met. If you wish your action to be carried out at a later time, you can specify the number of days here."
                                                           data-original-title="" title=""></i>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-1">
                                            <div class="row">
                                                <div class="col-md-12 hidden plusMinus" id="plusMinus_1">
                                                    <div class="row">
                                                    <span class="col-md-3 col-form-label">
                                                    <a href="javascript:void(0)" class="minus hidden" id="minus_1">
                                                        <i class="tooltipAjax fa fa-minus-circle text-danger" title=""
                                                           data-original-title="Click to remove condition"
                                                           aria-hidden="true"></i>
                                                    </a>
                                                    </span>
                                                        <span class="col-md-3 col-form-label">
                                                    <a href="javascript:void(0)" class="plus" id="plus_1">
                                                        <i class="tooltipAjax fa fa-plus-circle text-primary" title=""
                                                           data-original-title="Click to add condition"
                                                           aria-hidden="true"></i>
                                                    </a>
                                                </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                        <div id="newParameterDivAppendHere"></div>
                        <div class="clearfix"></div>
                        <div class="col-md-12 hidden" id="doneDiv">
                            <div class="text-right">
                                <button type="button" name="done" id="done" class="btn btn-primary">Done</button>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                </div>
            <?php } else { // edit/update rule ?>
                <div class="col-md-12 pl-0" id="conditions">
                    <div class="card card-custom alert alert-custom alert-default">
                        <?php
                        if ($ruleCondCnt > 0) {
                            $tblARId = $getRuleCondDetails['tblARId'];
                            $noOfDayStatus = $statusId = '';
                            for ($r = 1; $r <= 3; $r++) {
                                $ruleFor = $ruleForNx = $isIsNot = $conditionType = '';
                                $params = [];
                                $nx = $r + 1;
                                if (array_key_exists('ruleFor' . $r, $getRuleCondDetails)) $ruleFor = $getRuleCondDetails['ruleFor' . $r];
                                if (array_key_exists('ruleFor' . $nx, $getRuleCondDetails)) $ruleForNx = $getRuleCondDetails['ruleFor' . $nx];
                                if (array_key_exists('isIsNot' . $r, $getRuleCondDetails)) {
                                    $isIsNot = $getRuleCondDetails['isIsNot' . $r];
                                    if ($isIsNot == '') $isIsNot = 'IS'; //for old rules
                                }
                                if (array_key_exists('params' . $r, $getRuleCondDetails)) $params = explode('~', $getRuleCondDetails['params' . $r]);

                                if (array_key_exists('noOfDayStatus' . $r, $getRuleCondDetails)) $noOfDayStatus = $getRuleCondDetails['noOfDayStatus' . $r];
                                if (array_key_exists('changeStatus' . $r, $getRuleCondDetails)) $statusId = $getRuleCondDetails['changeStatus' . $r];
                                if ($r < 3) {
                                    if (array_key_exists('conditionType' . $r, $getRuleCondDetails)) $conditionType = $getRuleCondDetails['conditionType' . $r];
                                }
                                //Get the select options and then assign the selected values
                                //PFS
                                $fileStatusArray = [];
                                if ($ruleFor == automationConstants::$automation_PFS) {
                                    $inArray = ['PCID' => $assignedPCID, 'opt1' => 'list', 'opt2' => 'Y', 'searchTerm' => $fileType];
                                    $fileStatusArray = getPCPrimaryStatus::getReport($inArray);
                                    $ruleForOptions = $fileStatusArray['primaryStatusInfo'][$fileType];
                                }
                                //FSS
                                $PCFileSubstatusArray = [];
                                if ($ruleFor == automationConstants::$automation_FSS) {
                                    $inArray = ['PCID' => $assignedPCID, 'opt1' => 'list', 'opt2' => 'Y', 'searchTerm' => $fileType];
                                    $result = getPCFileSubstatus::getReport($inArray);
                                    $PCFileSubstatusArray = $result['substatusInfo'][$fileType];
                                    $PCFileSubstatusArray = Arrays::buildKeyByValue($PCFileSubstatusArray, 'PSCID');
                                }
                                //Workflow
                                $PCWFStepsArray = [];
                                if ($ruleFor == automationConstants::$automation_Workflow) {
                                    $inArray = ['PCID' => $assignedPCID];
                                    $PCWFStepsArray = getPCWFSteps::getReport($inArray);
                                }
                                //Branch
                                $branchData = [];
                                if ($ruleFor == automationConstants::$automation_Branch) {
                                    $branchArray = tblBranch::GetAll([
                                        'processingCompanyId' => $assignedPCID,
                                        'activeStatus' => 1,
                                    ]);
                                    foreach ($branchArray as $branch) {
                                        $branchData[] = [
                                            'id' => $branch->executiveId,
                                            'text' => $branch->LMRExecutive,
                                        ];
                                    }
                                }
                                //Broker
                                $brokersData = [];
                                if ($ruleFor == automationConstants::$automation_Broker) {
                                    $brokers = listAllAgents::getObjects([
                                        'PCID' => $assignedPCID,
                                        'externalBroker' => 0,
                                    ]);
                                    foreach ($brokers as $broker) {
                                        $brokersData[] = [
                                            'id' => $broker->brokerNumber,
                                            'text' => $broker->brokerName,
                                        ];
                                    }
                                }
                                //LO
                                $losData = [];
                                if ($ruleFor == automationConstants::$automation_LO) {
                                    $los = listAllAgents::getObjects([
                                        'PCID' => $assignedPCID,
                                        'externalBroker' => 1, //LO
                                    ]);
                                    foreach ($los as $lo) {
                                        $losData[] = [
                                            'id' => $lo->brokerNumber,
                                            'text' => $lo->brokerName,
                                        ];
                                    }
                                }
                                //Borrower Status
                                $borrowerStatusArray = [];
                                if ($ruleFor == automationConstants::$automation_BorrowerStatus) {
                                    $borrowerStatusArray = borrowerStatusArray::$borrowerStatusArray;
                                }
                                //Borrower Type
                                $borrowerTypeArray = [];
                                if ($ruleFor == automationConstants::$automation_BorrowerType) {
                                    $borrowerTypeArray = glBorrowerType::$glBorrowerTypeArray;
                                }
                                //Draw Request Status (for Initial SOW, Draw Request, and Revision)
                                $drawRequestStatusArray = [];
                                if ($ruleFor == automationConstants::$automation_InitialSOW ||
                                    $ruleFor == automationConstants::$automation_DrawRequest ||
                                    $ruleFor == automationConstants::$automation_Revision) {
                                    $drawRequestStatusArray = drawRequestStatusArray::$drawRequestStatusArray;
                                }
                                //Loan Program
                                $loanProgramArray = [];
                                if ($ruleFor == automationConstants::$automation_LoanProgram) {
                                    $loanProgramArray = getPCServiceType::getReport([
                                        'PCID'                    => $assignedPCID,
                                        'moduleCode'              => $fileType,
                                        'keyNeeded'               => 'n',
                                        'marketPlaceStatus'       => 'all',
                                        'marketPlacePublicStatus' => 'all',
                                    ]);
                                }
                                if ($ruleFor != '' && count($params) > 0) {
                                    ?>
                                    <div class="col-md-12 pl-0 row parameterDivs"
                                         id="newParameterDiv_<?php echo $r; ?>">
                                        <div class="col-md-6" id="parameter_<?php echo $r; ?>">
                                            <div class="form-group row">
                                                <label for="ruleFor_<?php echo $r; ?>"
                                                       class="col-md-5 font-weight-bold">Rule triggered when
                                                    <i class="popoverClass fas fa-info-circle text-primary"
                                                       data-html="true" data-placement="bottom"
                                                       data-content="<?php echo automationConstants::$automation_rule_trigger_tooltip; ?>"></i>
                                                </label>
                                                <div class="col-md-7">
                                                    <select name="ruleFor[]" id="ruleFor_<?php echo $r; ?>"
                                                            class="ruleFor form-control input-sm">
                                                        <option value="">-Select-</option>
                                                        <option value="FCU" <?php echo Arrays::isSelected(automationConstants::$automation_FCU, $ruleFor); ?>>
                                                            <?php echo automationConstants::$automation_LoanFileStatusTitle; ?>
                                                        </option>
                                                        <option value="PFS" <?php echo Arrays::isSelected(automationConstants::$automation_PFS, $ruleFor); ?>>
                                                            <?php echo automationConstants::$automation_PrimaryFileStatus; ?>
                                                        </option>
                                                        <option value="FSS" <?php echo Arrays::isSelected(automationConstants::$automation_FSS, $ruleFor); ?>>
                                                            <?php echo automationConstants::$automation_FileSubStatus; ?>
                                                        </option>
                                                        <option value="Workflow" <?php echo Arrays::isSelected(automationConstants::$automation_Workflow, $ruleFor); ?>>
                                                            <?php echo automationConstants::$automation_Workflow; ?>
                                                        </option>
                                                        <option value="<?php echo automationConstants::$automation_Branch; ?>"
                                                            <?php echo Arrays::isSelected(automationConstants::$automation_Branch, $ruleFor); ?>>
                                                            <?php echo automationConstants::$automation_Branch; ?>
                                                        </option>
                                                        <option value="<?php echo automationConstants::$automation_Broker; ?>"
                                                            <?php echo Arrays::isSelected(automationConstants::$automation_Broker, $ruleFor); ?>>
                                                            <?php echo automationConstants::$automation_Broker; ?>
                                                        </option>
                                                        <option value="<?php echo automationConstants::$automation_LO; ?>"
                                                            <?php echo Arrays::isSelected(automationConstants::$automation_LO, $ruleFor); ?>>
                                                            <?php echo automationConstants::$automation_LoanOfficer; ?>
                                                        </option>
                                                        <option value="<?php echo automationConstants::$automation_BorrowerStatus; ?>"
                                                            <?php echo Arrays::isSelected(automationConstants::$automation_BorrowerStatus, $ruleFor); ?>>
                                                            <?php echo automationConstants::$automation_BorrowerStatus_Title; ?>
                                                        </option>
                                                        <option value="<?php echo automationConstants::$automation_BorrowerType; ?>"
                                                            <?php echo Arrays::isSelected(automationConstants::$automation_BorrowerType, $ruleFor); ?>>
                                                            <?php echo automationConstants::$automation_BorrowerType_Title; ?>
                                                        </option>
                                                        <option value="<?php echo automationConstants::$automation_LoanProgram; ?>"
                                                            <?php echo Arrays::isSelected(automationConstants::$automation_LoanProgram, $ruleFor); ?>>
                                                            <?php echo automationConstants::$automation_LoanProgram_Title; ?>
                                                        </option>
                                                        <option value="<?php echo automationConstants::$automation_InitialSOW; ?>"
                                                            <?php echo Arrays::isSelected(automationConstants::$automation_InitialSOW, $ruleFor); ?>>
                                                            <?php echo automationConstants::$automation_InitialSOW_Title; ?>
                                                        </option>
                                                        <option value="<?php echo automationConstants::$automation_DrawRequest; ?>"
                                                            <?php echo Arrays::isSelected(automationConstants::$automation_DrawRequest, $ruleFor); ?>>
                                                            <?php echo automationConstants::$automation_DrawRequest_Title; ?>
                                                        </option>
                                                        <option value="<?php echo automationConstants::$automation_Revision; ?>"
                                                            <?php echo Arrays::isSelected(automationConstants::$automation_Revision, $ruleFor); ?>>
                                                            <?php echo automationConstants::$automation_Revision_Title; ?>
                                                        </option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6 andOr" id="andOr_<?php echo $r; ?>">
                                                <?php if ($r < 3) {
                                                    if (!($ruleForNx)) {
                                                        $conditionType = '';
                                                    }
                                                    ?>
                                                    <div class="col-md-6 text-center">
                                                        <input type="hidden" name="conditionType[]"
                                                               id="conditionType_<?php echo $r; ?>" class=""
                                                               value="<?php echo htmlspecialchars($conditionType); ?>">
                                                        <label><h2 class="is andOrTxt"
                                                                   id="andOrTxt_<?php echo $r; ?>"><?php echo htmlspecialchars($conditionType); ?></h2>
                                                        </label>
                                                    </div>
                                                <?php } ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6 row">
                                            <div class="col-md-1" id="isDiv_<?php echo $r; ?>">
                                                <div class="form-group row">
                                                    <input type="hidden" name="isIsNot[]" id="isIsNot_<?php echo $r; ?>"
                                                           class="isIsNotVal"
                                                           value="<?php echo htmlspecialchars($isIsNot); ?>">
                                                    <label class="col-form-label"><h6 class="is isIsNot text-primary"
                                                                                      id="isIsNotId_<?php echo $r; ?>"><?php echo htmlspecialchars($isIsNot); ?></h6>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-11">
                                                <div class="form-group row">
                                                    <div class="col-md-6" id="parameterValue_<?php echo $r; ?>">
                                                        <select name="parameterOptions_<?php echo $r; ?>[]"
                                                                id="parameterOptions_<?php echo $r; ?>"
                                                                style="width: 280px"
                                                                class="parameterValue js-example-basic-multiple"
                                                                multiple="multiple">
                                                            <?php
                                                            if ($ruleFor == automationConstants::$automation_FCU) {
                                                                foreach (automationConstants::$automation_LoanFileStatus as $alfsKey => $alfsValue) { ?>
                                                                    <option value="<?php echo $alfsKey; ?>" <?php if (in_array($alfsKey, $params)) echo 'selected=selected'; ?> ><?php echo $alfsValue; ?></option>
                                                                    <?php
                                                                }
                                                            }
                                                            if ($ruleFor == automationConstants::$automation_PFS) {
                                                                foreach ($ruleForOptions as $pfs) { ?>
                                                                    <option value="<?php echo htmlspecialchars($pfs['PSID']); ?>" <?php if (in_array($pfs['PSID'], $params)) echo 'selected=selected'; ?> ><?php echo htmlspecialchars($pfs['primaryStatus']); ?></option>
                                                                    <?php
                                                                }
                                                            }
                                                            if ($ruleFor == automationConstants::$automation_FSS) {
                                                                foreach ($PCFileSubstatusArray as $p => $fss) {
                                                                    $b = 0;
                                                                    $optGroupLabelFss = $fss[$b]['category'];
                                                                    echo "<optgroup label='" . $optGroupLabelFss . "'>";
                                                                    foreach ($fss as $fileSubStatus) {
                                                                        $pfsid = $substatus = '';
                                                                        $pfsid = $fileSubStatus['PFSID'];
                                                                        $substatus = $fileSubStatus['substatus'];
                                                                        ?>
                                                                        <option value="<?php echo $pfsid; ?>" <?php if (in_array($pfsid, $params)) echo 'selected=selected'; ?> ><?php echo $substatus; ?></option>
                                                                        <?php
                                                                    }
                                                                    echo '</optgroup>';
                                                                    $b++;
                                                                }
                                                            }
                                                            if ($ruleFor == automationConstants::$automation_Workflow) {
                                                                foreach ($PCWFStepsArray as $q => $wf) {
                                                                    $a = 0;
                                                                    $optGroupLabel = $wf[$a]['WFName'];
                                                                    echo "<optgroup label='" . $optGroupLabel . "'>";
                                                                    foreach ($wf as $workflow) {
                                                                        $wfsId = $wfSteps = '';
                                                                        $wfsId = $workflow['WFSID'];
                                                                        $wfSteps = $workflow['steps'];
                                                                        ?>
                                                                        <option value="<?php echo $wfsId; ?>" <?php if (in_array($wfsId, $params)) echo 'selected=selected'; ?> ><?php echo $wfSteps; ?></option>
                                                                        <?php
                                                                    }
                                                                    echo '</optgroup>';
                                                                    $a++;
                                                                }
                                                            }
                                                            if ($ruleFor == automationConstants::$automation_Branch) {
                                                                foreach ($branchData as $branch) {
                                                                    ?>
                                                                    <option value="<?php echo $branch['id']; ?>" <?php if (in_array($branch['id'], $params)) echo 'selected=selected'; ?> ><?php echo $branch['text']; ?></option>
                                                                    <?php
                                                                }
                                                            }
                                                            if ($ruleFor == automationConstants::$automation_Broker) {
                                                                foreach ($brokersData as $broker) {
                                                                    ?>
                                                                    <option value="<?php echo $broker['id']; ?>" <?php if (in_array($broker['id'], $params)) echo 'selected=selected'; ?> ><?php echo $broker['text']; ?></option>
                                                                    <?php
                                                                }
                                                            }
                                                            if ($ruleFor == automationConstants::$automation_LO) {
                                                                foreach ($losData as $lo) {
                                                                    ?>
                                                                    <option value="<?php echo $lo['id']; ?>" <?php if (in_array($lo['id'], $params)) echo 'selected=selected'; ?> ><?php echo $lo['text']; ?></option>
                                                                    <?php
                                                                }
                                                            }
                                                            if ($ruleFor == automationConstants::$automation_BorrowerStatus) {
                                                                foreach ($borrowerStatusArray as $bsKey => $bsValue) {
                                                                    ?>
                                                                    <option value="<?php echo $bsKey; ?>" <?php if (in_array($bsKey, $params)) echo 'selected=selected'; ?> ><?php echo $bsValue; ?></option>
                                                                    <?php
                                                                }
                                                            }
                                                            if ($ruleFor == automationConstants::$automation_BorrowerType) {
                                                                foreach ($borrowerTypeArray as $btKey => $btValue) {
                                                                    ?>
                                                                    <option value="<?php echo $btValue; ?>" <?php if (in_array($btValue, $params)) echo 'selected=selected'; ?> ><?php echo $btValue; ?></option>
                                                                    <?php
                                                                }
                                                            }
                                                            if ($ruleFor == automationConstants::$automation_InitialSOW ||
                                                                $ruleFor == automationConstants::$automation_DrawRequest ||
                                                                $ruleFor == automationConstants::$automation_Revision) {
                                                                foreach ($drawRequestStatusArray as $drsKey => $drsValue) {
                                                                    ?>
                                                                    <option value="<?php echo $drsKey; ?>" <?php if (in_array($drsKey, $params)) echo 'selected=selected'; ?> ><?php echo $drsValue; ?></option>
                                                                    <?php
                                                                }
                                                            }
                                                            if ($ruleFor == automationConstants::$automation_LoanProgram) {
                                                                foreach ($loanProgramArray as $serviceType) {
                                                                    $serviceTypeCode = $serviceType['LMRClientType'];
                                                                    $serviceTypeName = $serviceType['serviceType'];
                                                                    ?>
                                                                    <option value="<?php echo $serviceTypeCode; ?>" <?php if (in_array($serviceTypeCode, $params)) echo 'selected=selected'; ?> ><?php echo $serviceTypeName; ?></option>
                                                                    <?php
                                                                }
                                                            }
                                                            ?>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-4 noOfDayStatusDiv <?php if (!($r == 1 && $ruleFor == automationConstants::$automation_PFS)) {
                                                        echo 'hidden';
                                                    } ?>" id="noOfDayStatusDiv">
                                                        <div class="row">
                                                            <div class="input-group">
                                                                <span class="input-group-text">For</span>
                                                                <input type="text" name="noOfDayStatus"
                                                                       id="noOfDayStatus" maxlength="3"
                                                                       class="form-control" autocomplete="off"
                                                                       value="<?php echo htmlspecialchars($noOfDayStatus); ?>">
                                                                <span class="input-group-text">
                                                                        Day(s) &nbsp;
                                                                    <i class="popoverClass fas fa-info-circle text-primary"
                                                                       data-html="true"
                                                                       data-placement="bottom"
                                                                       data-content="This field can be left as 0 to carry out the expected action as soon as the conditions are met. If you wish your action to be carried out at a later time, you can specify the number of days here."
                                                                       data-original-title=""
                                                                       title=""></i>
                                                                    </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="row">
                                                            <div class="col-md-12 plusMinus"
                                                                 id="plusMinus_<?php echo $r; ?>">
                                                                <div class="row">
                                                                    <span class="col-md-3 col-form-label">
                                                                        <a href="javascript:void(0)" class="minus"
                                                                           id="minus_<?php echo $r; ?>">
                                                                            <i class="tooltipAjax fa fa-minus-circle text-danger"
                                                                               title=""
                                                                               data-original-title="Click to remove condition"
                                                                               rel="tooltip" aria-hidden="true"></i>
                                                                        </a>
                                                                    </span>
                                                                    <span class="col-md-3 col-form-label">
                                                                        <a href="javascript:void(0)" class="plus"
                                                                           id="plus_<?php echo $r; ?>">
                                                                            <i class="tooltipAjax fa fa-plus-circle text-primary"
                                                                               title=""
                                                                               data-original-title="Click to add condition"
                                                                               rel="tooltip" aria-hidden="true"></i>
                                                                        </a>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                }
                            }
                        }
                        ?>
                        <div class="clearfix"></div>
                        <div id="newParameterDivAppendHere"></div>
                        <div class="clearfix"></div>
                        <div class="col-md-12 hidden" id="doneDiv">
                            <div class="text-right">
                                <button type="button" name="done" id="done" class="btn btn-primary">Done</button>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                </div>
            <?php } // end add/edit/update conditions ?>
            <div class="clearfix"></div>
            <div class="col-md-12 row  <?php if ($id == '' && $showActionDiv == 0) echo 'hidden'; ?> " id="actions">
                <div class="col-md-6 pl-0">
                    <div class="card card-custom">
                        <div class="card-header card-header-tabs-line bg-gray-100">
                            <div class="card-title">
                                <h5 class="card-label"><i class="fa fa-bolt" aria-hidden="true"></i> Instant Actions
                                </h5>
                            </div>
                        </div>
                        <div class="card-body m-0">
                            <?php
                            if ($id > 0 && !$statusId) {
                                $statusId = getAutomatedRuleCFS::getChangeFileStatusId($assignedPCID, $id);
                            }
                            ?>
                            <input type="hidden" id="statusUpdateId" name="statusUpdateId" value="<?php echo $statusId; ?>">
                            <div id="instantActionDiv">
                                <div id="changeStatusDiv" class="col-md-12 mb-3">
                                    <label class="font-weight-bold">Change File Status</label>
                                    <div class="col-md-12 pl-0">
                                        <ul class="pl-5">
                                            <?php
                                            //old status
                                            $oldStatusIds = [];
                                            $oldStatusTxt = $newStatusTxt = '';
                                            if (count($getRuleCondDetails) > 0) {
                                                $oldStatusIds = $getRuleCondDetails['params1'];
                                                $oldStatusIds = explode('~', $oldStatusIds);
                                                foreach ($ruleForOptions as $pfStsId) {
                                                    //old status
                                                    if (in_array($pfStsId['PSID'], $oldStatusIds)) {
                                                        $oldStatusTxt .= $pfStsId['primaryStatus'] . ', ';
                                                    }
                                                    //new status
                                                    if ($pfStsId['PSID'] == $statusId) {
                                                        $newStatusTxt = $pfStsId['primaryStatus'];
                                                    }
                                                }
                                                if ($changeFileStatus) {
                                                    $newStatusTxt = $changeFileStatus;
                                                }
                                            }
                                            if ($newStatusTxt != '') {
                                                if (!$oldStatusTxt) {
                                                    $oldStatusTxt = 'Any Status ';
                                                }
                                                ?>
                                                <li class="">
                                                    <span class="font-weight-bold">From: <?php echo htmlspecialchars($oldStatusTxt); ?></span>
                                                    <span class="font-weight-bold newStatusTxt">To: <?php echo htmlspecialchars($newStatusTxt); ?></span>
                                                </li>
                                            <?php } ?>
                                            <?php if ($id > 0 && !$newStatusTxt) { ?>
                                                <li> - No Status Change added for this rule</li>
                                            <?php } ?>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <label class="font-weight-bold">Instant Task(s)</label>
                                    <div class="col-md-12 pl-0">
                                        <ul class="pl-5">
                                            <?php foreach ($Tasks as $task) {
                                                $tkStatus = $task['status'] == 1 ? 'Enabled' : 'Disabled';
                                                $tkClass = $task['status'] == 1 ? 'text-success' : 'text-danger';
                                                ?>
                                                <li class="<?php echo $tkClass; ?>"><?php echo htmlspecialchars($task['taskSubject']) . ' (' . htmlentities($tkStatus) . ')'; ?></li>
                                            <?php } ?>
                                            <?php if ($id > 0 && count($Tasks) == 0) { ?>
                                                <li> - No Task(s) added for this rule</li>
                                            <?php } ?>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <label class="font-weight-bold">Instant Email Notification(s)</label>
                                    <div class="col-md-12 pl-0">
                                        <ul class="pl-5">
                                            <?php foreach ($Emails as $email) {
                                                $emStatus = $email['status'] == 1 ? 'Enabled' : 'Disabled';
                                                $emClass = $email['status'] == 1 ? 'text-success' : 'text-danger';
                                                ?>
                                                <li class="<?php echo $emClass; ?>"><?php echo htmlspecialchars($email['emailSubject']) . ' (' . htmlentities($emStatus) . ')'; ?></li>
                                            <?php } ?>
                                            <?php if ($id > 0 && count($Emails) == 0) { ?>
                                                <li> - No Email Notification(s) added for this rule</li>
                                            <?php } ?>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <label class="font-weight-bold">Instant Webhook(s)</label>
                                    <div class="col-md-12 pl-0">
                                        <ul class="pl-5">
                                            <?php foreach ($Webhooks as $webhook) {
                                                $whStatus = $webhook['activeStatus'] == 1 ? 'Enabled' : 'Disabled';
                                                $whClass = $webhook['activeStatus'] == 1 ? 'text-success' : 'text-danger';
                                                ?>
                                                <li class="<?php echo $whClass; ?>"><?php echo htmlentities($webhook['webhookName']) . ' (' . htmlspecialchars($whStatus) . ') '; ?></li>
                                            <?php } ?>
                                            <?php if ($id > 0 && count($Webhooks) == 0) { ?>
                                                <li> - No Webhook(s) added for this rule</li>
                                            <?php } ?>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <label class="font-weight-bold">Assign Back Office User(s)</label>
                                    <div class="col-md-12 pl-0">
                                        <ul class="pl-5">
                                            <?php
                                            if($UsersId) {
                                            $employees = models\composite\oEmployee\getMyDetails::getReportObjects($UsersId);
                                            foreach ($employees as $empKey => $empValue) {
                                                $empClass = $empValue->empActiveStatus == 1 ? 'text-success' : 'text-danger';
                                                ?>
                                                <li class="<?php echo $empClass; ?>"><?php echo $empValue->employeeNames; ?></li>
                                            <?php } } else { ?>
                                                 <li> - No Assign Back Office User(s) added for this rule</li>
                                            <?php } ?>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="separator separator-dashed separator-border-2 separator-primary mb-5"></div>
                            <div class="col-md-12 pl-0">
                                <ul class="nav nav-pills">
                                    <li class="nav-item dropdown active">
                                        <a class="nav-link dropdown-toggle active" data-toggle="dropdown" href="#"
                                           role="button" aria-haspopup="true" aria-expanded="false">
                                            + Action(s)
                                        </a>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item actionStatus"
                                               href='#' id="actionStatusLink"
                                               data-href='<?php echo CONST_URL_POPS; ?>automatedRulesInstantAction.php'
                                               data-wsize='modal-xl'
                                               data-name='Change Status'
                                               data-toggle='modal' data-target='#exampleModal1'
                                               data-id='PCID=<?php echo cypher::myEncryption($assignedPCID); ?>&action=Status'
                                               title='Change Status'>
                                                Change Status
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item actionTask"
                                               href='#'
                                               data-href='<?php echo CONST_URL_POPS; ?>automatedRulesInstantAction.php'
                                               data-wsize='modal-xl'
                                               data-name='Assign Instant Task'
                                               data-toggle='modal' data-target='#exampleModal1'
                                               data-id='PCID=<?php echo cypher::myEncryption($assignedPCID); ?>&action=Task'
                                               title='Assign Instant Task'>
                                                Task
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item actionEmail"
                                               href="#"
                                               data-href='<?php echo CONST_URL_POPS; ?>automatedRulesInstantAction.php'
                                               data-wsize='modal-xl'
                                               data-name='Assign Instant Email'
                                               data-toggle='modal' data-target='#exampleModal1'
                                               data-id='PCID=<?php echo cypher::myEncryption($assignedPCID); ?>&action=Email'
                                               title='Assign Instant Email'>
                                                Email Notification
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item actionWebhook"
                                               href="#"
                                               data-href='<?php echo CONST_URL_POPS; ?>automatedRulesInstantAction.php'
                                               data-wsize='modal-xl'
                                               data-name='Assign Instant Webhook'
                                               data-toggle='modal' data-target='#exampleModal1'
                                               data-id='PCID=<?php echo cypher::myEncryption($assignedPCID); ?>&action=Webhook'
                                               title='Assign Instant Webhook'>
                                                Webhook
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item actionUser"
                                               href="#"
                                               data-href='<?php echo CONST_URL_POPS; ?>automatedRulesInstantAction.php'
                                               data-wsize='modal-xl'
                                               data-name='Assign Back Office User(s)'
                                               data-toggle='modal' data-target='#exampleModal1'
                                               data-id='PCID=<?php echo cypher::myEncryption($assignedPCID); ?>&action=User'
                                               title='Assign Back Office User(s)'>
                                                Assign Back Office User(s)
                                            </a>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 pr-0">
                    <div class="card card-custom">
                        <div class="card-header card-header-tabs-line bg-gray-100">
                            <div class="card-title">
                                <h5 class="card-label"><i class="fa fa-hourglass-half" aria-hidden="true"></i> Scheduled
                                    Actions </h5>
                            </div>
                        </div>
                        <div class="card-body m-0">
                            <div id="scheduleActionDiv">
                                <div class="col-md-12">
                                    <label class="font-weight-bold">Scheduled Task(s)</label>
                                    <div class="col-md-12 pl-0">
                                        <?php
                                        $day = $noOfDays = $eventWhen = $taskReferralDate = '';
                                        $scheduleON = '';
                                        foreach ($ScheduleTasks as $schtask) {
                                            $tsid = $schtask['id'];
                                            $tkSchStatus = $schtask['status'] == 1 ? 'Enabled' : 'Disabled';
                                            $tkSchClass = $schtask['status'] == 1 ? 'text-success' : 'text-danger';
                                            $contEventStatusTask = $schtask['contEventStatus'];
                                            //Task Scheduled ON
                                            $day = $schtask['noOfDays'] == 1 ? ' day' : ' day(s)';
                                            $noOfDays = $schtask['noOfDays'] . $day;
                                            $eventWhen = $schtask['eventWhen'] != '' ? $schtask['eventWhen'] : '';
                                            $taskReferralDateValue = $schtask['referralDate'];
                                            if ($taskReferralDateValue != '') {
                                                $taskReferralDateExplode = explode('-', $taskReferralDateValue);
                                                $taskReferralDateCount = count($taskReferralDateExplode);
                                                if ($taskReferralDateCount == 1 && !in_array($taskReferralDateValue, $glEventReferralDateAllFileTypeArrayKeys)) { // old value - append file type
                                                    $taskReferralDateValue = $RDFT . $taskReferralDateValue;
                                                }
                                            }
                                            $taskReferralDate = $taskReferralDateValue != '' ? $glEventReferralDateArray[$taskReferralDateValue] : '';
                                            if ($eventWhen == 'ON') {
                                                $scheduleON = $taskReferralDate;
                                            } else {
                                                $scheduleON = $noOfDays . ' ' . $eventWhen . ' ' . $taskReferralDate;
                                            }
                                            ?>
                                            <div class="row">
                                                <div class="col-md-6 <?php echo $tkSchClass; ?>">
                                                    <div class="checkbox-inline">
                                                        <label class="checkbox popoverAjax <?php if ($tkSchStatus == 'Disabled') {
                                                            echo 'checkbox-disabled';
                                                        } ?> " <?php if ($tkSchStatus != 'Disabled') { ?>  data-html="true" data-placement="left" data-trigger="hover"
                                                            data-content="Check this box to continue to process this automated action, even if you change this rule's configuration." data-toggle="popover" <?php } ?> >
                                                            <input type="checkbox"
                                                                   id="sch_ts_<?php echo htmlspecialchars($tsid); ?>"
                                                                   class="contEventStatus schTsCe clRedTxt" <?php if ($tkSchStatus == 'Disabled') {
                                                                echo 'disabled="disabled"';
                                                            } ?> <?php if ($contEventStatusTask == 1) {
                                                                echo 'checked=checked';
                                                            } ?> name="contEventStatusTask[]"
                                                                   value="<?php echo htmlspecialchars($schtask['tblARESTId']); ?>">
                                                            <span></span>
                                                        </label>
                                                        <?php echo htmlspecialchars($schtask['taskSubject']) . ' (' . htmlspecialchars($tkSchStatus) . ')'; ?>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <span>(Scheduled ON: <?php echo htmlspecialchars($scheduleON); ?>)</span>
                                                    <span id="txt_sch_ts_<?php echo htmlspecialchars($tsid); ?>"
                                                          class="text-danger"><?php if ($contEventStatusTask == 1) { ?> (continue event after conditions no longer match) <?php } ?></span>
                                                </div>
                                            </div>
                                        <?php } ?>
                                        <?php if ($id > 0 && count($ScheduleTasks) == 0) { ?>
                                            <ul class="pl-5">
                                                <li> - No Task(s) added for this rule</li>
                                            </ul>
                                        <?php } else { ?>
                                            <ul></ul>
                                        <?php } ?>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <label class="font-weight-bold">Scheduled Email Notification(s)</label>
                                    <div class="col-md-12 pl-0">
                                        <?php
                                        $day = $noOfDays = $eventWhen = $emailReferralDate = '';
                                        $scheduleON = '';
                                        foreach ($ScheduleEmails as $schemail) {
                                            $emid = $schemail['id'];
                                            $emSchStatus = $schemail['status'] == 1 ? 'Enabled' : 'Disabled';
                                            $emSchClass = $schemail['status'] == 1 ? 'text-success' : 'text-danger';
                                            $contEventStatusEmail = $schemail['contEventStatus'];
                                            //Email Scheduled ON
                                            $day = $schemail['noOfDays'] == 1 ? ' day' : ' day(s)';
                                            $noOfDays = $schemail['noOfDays'] . $day;
                                            $eventWhen = $schemail['eventWhen'] != '' ? $schemail['eventWhen'] : '';
                                            $emailReferralDateValue = $schemail['referralDate'];
                                            if ($emailReferralDateValue != '') {
                                                $emailReferralDateExplode = explode('-', $emailReferralDateValue);
                                                $emailReferralDateCount = count($emailReferralDateExplode);
                                                if ($emailReferralDateCount == 1 && !in_array($emailReferralDateValue, $glEventReferralDateAllFileTypeArrayKeys)) { // old value - append file type
                                                    $emailReferralDateValue = $RDFT . $emailReferralDateValue;
                                                }
                                            }
                                            $emailReferralDate = $emailReferralDateValue != '' ? $glEventReferralDateArray[$emailReferralDateValue] : '';
                                            if ($eventWhen == 'ON') {
                                                $scheduleON = $emailReferralDate;
                                            } else {
                                                $scheduleON = $noOfDays . ' ' . $eventWhen . ' ' . $emailReferralDate;
                                            }
                                            ?>
                                            <div class="row">
                                                <div class="col-md-6  <?php echo $emSchClass; ?>">
                                                    <div class="checkbox-inline">
                                                        <label class="checkbox popoverAjax <?php if ($emSchStatus == 'Disabled') {
                                                            echo 'checkbox-disabled';
                                                        } ?> " <?php if ($emSchStatus != 'Disabled') { ?>  data-html="true" data-placement="left" data-trigger="hover"
                                                            data-content="Check this box to continue to process this automated action, even if you change this rule's configuration." data-toggle="popover" <?php } ?> >
                                                            <input type="checkbox"
                                                                   id="sch_em_<?php echo htmlspecialchars($emid); ?>"
                                                                   class="contEventStatus schEmCe clRedTxt" <?php if ($emSchStatus == 'Disabled') {
                                                                echo 'disabled="disabled"';
                                                            } ?> <?php if ($contEventStatusEmail == 1) {
                                                                echo 'checked=checked';
                                                            } ?> name="contEventStatusEmail[]"
                                                                   value="<?php echo htmlspecialchars($schemail['tblARESEId']); ?>">
                                                            <span></span>
                                                        </label>
                                                        <?php echo htmlspecialchars($schemail['emailSubject']) . ' (' . htmlspecialchars($emSchStatus) . ')'; ?>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <span>(Scheduled ON: <?php echo htmlspecialchars($scheduleON); ?>)</span>
                                                    <span id="txt_sch_em_<?php echo htmlspecialchars($emid); ?>"
                                                          class="text-danger"><?php if ($contEventStatusEmail == 1) { ?>(continue event after conditions no longer match)<?php } ?></span>
                                                </div>
                                            </div>
                                        <?php } ?>
                                        <?php if ($id > 0 && count($ScheduleEmails) == 0) { ?>
                                            <ul class="pl-5">
                                                <li>- No Email Notification(s) added for this rule</li>
                                            </ul>
                                        <?php } else { ?>
                                            <ul></ul>
                                        <?php } ?>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <label class="font-weight-bold">Scheduled Webhook(s)</label>
                                    <div class="col-md-12 pl-0">
                                        <?php
                                        $day = $noOfDays = $eventWhen = $webhookReferralDate = '';
                                        $scheduleON = '';
                                        foreach ($ScheduleWebhooks as $schwebhook) {
                                            $whid = $schwebhook['WHID'];
                                            $whSchStatus = $schwebhook['activeStatus'] == 1 ? 'Enabled' : 'Disabled';
                                            $whSchClass = $schwebhook['activeStatus'] == 1 ? 'text-success' : 'text-danger';
                                            $contEventStatusWebhook = $schwebhook['contEventStatus'];
                                            //Webhook Scheduled ON
                                            $day = $schwebhook['noOfDays'] == 1 ? ' day' : ' day(s)';
                                            $noOfDays = $schwebhook['noOfDays'] . $day;
                                            $eventWhen = $schwebhook['eventWhen'] != '' ? $schwebhook['eventWhen'] : '';
                                            $webhookReferralDateValue = $schwebhook['referralDate'];
                                            if ($webhookReferralDateValue != '') {
                                                $webhookReferralDateExplode = explode('-', $webhookReferralDateValue);
                                                $webhookReferralDateCount = count($webhookReferralDateExplode);
                                                if ($webhookReferralDateCount == 1 && !in_array($webhookReferralDateValue, $glEventReferralDateAllFileTypeArrayKeys)) { // old value - append file type
                                                    $webhookReferralDateValue = $RDFT . $webhookReferralDateValue;
                                                }
                                            }
                                            $webhookReferralDate = $webhookReferralDateValue != '' ? $glEventReferralDateArray[$webhookReferralDateValue] : '';
                                            if ($eventWhen == 'ON') {
                                                $scheduleON = $webhookReferralDate;
                                            } else {
                                                $scheduleON = $noOfDays . ' ' . $eventWhen . ' ' . $webhookReferralDate;
                                            }
                                            ?>
                                            <div class="row">
                                                <div class="col-md-6 <?php echo $whSchClass; ?>">
                                                    <div class="checkbox-inline">
                                                        <label class="checkbox popoverAjax <?php if ($whSchStatus == 'Disabled') {
                                                            echo 'checkbox-disabled';
                                                        } ?> " <?php if ($whSchStatus != 'Disabled') { ?> data-html="true" data-placement="left" data-trigger="hover"
                                                            data-content="Check this box to continue to process this automated action, even if you change this rule's configuration." data-toggle="popover" <?php } ?> >
                                                            <input type="checkbox"
                                                                   id="sch_wh_<?php echo htmlspecialchars($whid); ?>"
                                                                   class="contEventStatus schWhCe clRedTxt" <?php if ($whSchStatus == 'Disabled') {
                                                                echo 'disabled="disabled"';
                                                            } ?>  <?php if ($contEventStatusWebhook == 1) {
                                                                echo 'checked=checked';
                                                            } ?> name="contEventStatusWebhook[]"
                                                                   value="<?php echo htmlspecialchars($schwebhook['tblARESWId']); ?>">
                                                            <span></span>
                                                        </label>
                                                        <?php echo htmlspecialchars($schwebhook['webhookName']) . ' (' . htmlspecialchars($whSchStatus) . ')'; ?>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <span>(Scheduled ON: <?php echo htmlspecialchars($scheduleON); ?>)</span>
                                                    <span id="txt_sch_wh_<?php echo htmlspecialchars($whid); ?>"
                                                          class="text-danger"><?php if ($contEventStatusWebhook == 1) { ?>(continue event after conditions no longer match)<?php } ?></span>
                                                </div>
                                            </div>
                                        <?php } ?>
                                        <?php if ($id > 0 && count($ScheduleWebhooks) == 0) { ?>
                                            <ul class="pl-5">
                                                <li>- No Webhook(s) added for this rule</li>
                                            </ul>
                                        <?php } else { ?>
                                            <ul></ul>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                            <div class="separator separator-dashed separator-border-2 separator-primary mb-5"></div>
                            <div class="col-md-12 pl-0">
                                <ul class="nav nav-pills">
                                    <li class="nav-item dropdown active">
                                        <a class="nav-link dropdown-toggle active" data-toggle="dropdown" href="#"
                                           role="button" aria-haspopup="true" aria-expanded="false">
                                            + Action(s)
                                        </a>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item actionScheduleTask"
                                               href="#"
                                               data-href='<?php echo CONST_URL_POPS; ?>automatedRulesScheduleAction.php'
                                               data-wsize='modal-xl'
                                               data-name='Assign Schedule Task'
                                               data-toggle='modal' data-target='#exampleModal1'
                                               data-id='PCID=<?php echo cypher::myEncryption($assignedPCID); ?>&schAction=Task'
                                               title='Assign Schedule Task'>
                                                Task
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item actionScheduleEmail"
                                               href="#"
                                               data-href='<?php echo CONST_URL_POPS; ?>automatedRulesScheduleAction.php'
                                               data-wsize='modal-xl'
                                               data-name='Assign Schedule Email'
                                               data-toggle='modal' data-target='#exampleModal1'
                                               data-id='PCID=<?php echo cypher::myEncryption($assignedPCID); ?>&schAction=Email'
                                               title='Assign Schedule Email'>
                                                Email Notification
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item actionScheduleWebhook"
                                               href="#"
                                               data-href='<?php echo CONST_URL_POPS; ?>automatedRulesScheduleAction.php'
                                               data-wsize='modal-xl'
                                               data-name='Assign Schedule Webhook'
                                               data-toggle='modal' data-target='#exampleModal1'
                                               data-id='PCID=<?php echo cypher::myEncryption($assignedPCID); ?>&schAction=Webhook'
                                               title='Assign Schedule Webhook'>
                                                Webhook
                                            </a>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="clearfix"></div>
            <div class="col-md-12 mt-10 <?php if (!($id > 0)) echo 'hidden'; ?>" id="btnSaveCancel">
                <div class="text-center">
                    <button type="submit" name="save" id="save" class="btn btn-primary">Save</button>
                    <button type="button" name="cancel" id="cancel" class="btn btn-primary">Cancel</button>
                </div>
            </div>
        </form>
    </div>
</div>
